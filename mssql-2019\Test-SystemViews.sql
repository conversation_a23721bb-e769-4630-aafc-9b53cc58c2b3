/*
================================================================================
SQL Server 2019 System Views Compatibility Test Script
================================================================================
Description: Test script to verify system view columns exist before running main audit
Version: 1.0
Created: September 16, 2025

INSTRUCTIONS:
Run this script first to verify system view compatibility before executing the main audit script.
This script tests the specific columns that were causing errors.
================================================================================
*/

SET NOCOUNT ON;

PRINT '================================================================================';
PRINT 'SQL Server 2019 System Views Compatibility Test';
PRINT 'Testing columns used in CIS Audit Script v1.2';
PRINT '================================================================================';
PRINT '';

-- Test 1: sys.dm_database_encryption_keys columns
PRINT 'TEST 1: Testing sys.dm_database_encryption_keys columns...';
BEGIN TRY
    SELECT TOP 1
        database_id,
        encryption_state,
        key_algorithm,  -- This should work (not encryption_algorithm_desc)
        create_date
    FROM sys.dm_database_encryption_keys;
    PRINT '✓ SUCCESS: sys.dm_database_encryption_keys.key_algorithm column exists';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: sys.dm_database_encryption_keys test failed';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH
PRINT '';

-- Test 2: sys.database_principals columns (without is_disabled)
PRINT 'TEST 2: Testing sys.database_principals columns...';
BEGIN TRY
    SELECT TOP 1
        name,
        principal_id,
        type_desc,
        create_date
        -- Note: is_disabled does NOT exist in sys.database_principals
    FROM sys.database_principals
    WHERE principal_id > 4;
    PRINT '✓ SUCCESS: sys.database_principals basic columns exist';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: sys.database_principals test failed';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH
PRINT '';

-- Test 3: sys.sql_logins columns (where is_disabled DOES exist)
PRINT 'TEST 3: Testing sys.sql_logins columns...';
BEGIN TRY
    SELECT TOP 1
        name,
        is_disabled,  -- This column exists in sys.sql_logins
        is_policy_checked,
        is_expiration_checked,
        modify_date
    FROM sys.sql_logins;
    PRINT '✓ SUCCESS: sys.sql_logins.is_disabled column exists';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: sys.sql_logins test failed';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH
PRINT '';

-- Test 4: Combined query similar to what's used in the audit script
PRINT 'TEST 4: Testing combined query logic...';
BEGIN TRY
    SELECT TOP 1
        d.name AS DatabaseName,
        dp.name AS UserName,
        dp.type_desc AS UserType,
        CASE
            WHEN sl.is_disabled = 1 THEN 'No'
            WHEN sl.is_disabled = 0 THEN 'Yes'
            ELSE 'Yes (Windows User)'
        END AS IsEnabled,
        dp.create_date AS UserCreateDate
    FROM sys.databases d
    CROSS APPLY (
        SELECT TOP 1 *
        FROM sys.database_principals dp
        WHERE dp.type IN ('S', 'U', 'G')
        AND dp.principal_id > 4
        AND dp.name NOT IN ('guest', 'INFORMATION_SCHEMA', 'sys')
    ) dp
    LEFT JOIN sys.sql_logins sl ON dp.sid = sl.sid
    WHERE d.database_id > 4
    AND d.state = 0;
    PRINT '✓ SUCCESS: Combined query logic works correctly';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: Combined query test failed';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH
PRINT '';

-- Test 5: TDE query similar to audit script
PRINT 'TEST 5: Testing TDE query logic...';
BEGIN TRY
    SELECT TOP 1
        db.name AS DatabaseName,
        ISNULL(dek.encryption_state, 0) AS EncryptionState,
        dek.key_algorithm AS EncryptionAlgorithm,
        dek.create_date AS EncryptionKeyCreateDate
    FROM sys.databases db
    LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id
    WHERE db.database_id > 4;
    PRINT '✓ SUCCESS: TDE query logic works correctly';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: TDE query test failed';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH
PRINT '';

PRINT '================================================================================';
PRINT 'System Views Compatibility Test Completed';
PRINT 'If all tests show SUCCESS, the main audit script should run without column errors';
PRINT '================================================================================';

/*
================================================================================
EXPECTED RESULTS:
- All 5 tests should show SUCCESS
- If any test shows ERROR, the main audit script will fail on those sections
- This test script is safe to run in production (read-only operations only)
================================================================================
*/
