#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.7.0
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Server Mailbox Security Audit Script
================================================================================
Description: Focused audit script for Exchange Server mailbox access and security controls

VERSION HISTORY:
================================================================================
Version 1.0 - August 17, 2025 - Initial Release
- Author: E.Z. Consultancy Development Team
- Basic mailbox security audit functionality
- 5 core security controls assessment
- JSON output format
- Production-safe read-only operations

Version 1.1 - August 18, 2025 - Performance Enhancements
- Improved error handling and logging
- Enhanced progress reporting
- Optimized mailbox permission queries
- Added sample size controls for large environments

Version 1.5 - August 21, 2025 - Domain Filtering Feature
- Added -DomainFilter parameter for targeted auditing
- Support for single and multiple domain filtering
- Enhanced performance for multi-domain environments
- Maintained backward compatibility

Version 1.6 - August 23, 2025 - Critical Bug Fixes and Performance Improvements
- Critical bug fixes and performance improvements
- Safe domain extraction functions with comprehensive error handling
- Enhanced error handling and validation throughout the script
- Optimized mailbox filtering with server-side filtering capabilities
- Improved cross-domain analysis validation and error reporting
- Comprehensive input parameter validation with regex pattern matching
- Enhanced production safety and reliability

Version 1.6.5 - August 25, 2025 - Final Production Release
- Fixed critical operator logic error (replaced -contains with -like operators)
- Resolved PowerShell hash table syntax compatibility issues
- Added comprehensive version tracking and display functionality
- Implemented cross-version PowerShell compatibility (5.1, 6.x, 7.x)
- Enhanced script startup with detailed environment information
- Added permanent version tracking in JSON audit metadata
- Improved error handling and syntax validation
- Final production-ready release with complete traceability

Version 1.6.6 - September 11, 2025 - Critical JSON Serialization and Domain Filtering Fixes
- CRITICAL: Fixed JSON serialization failure that prevented audit record generation
- CRITICAL: Resolved domain filtering logic error causing zero mailbox results

Version 1.6.7 - September 11, 2025 - Exchange 2016 Domain Filtering Compatibility Fix
- CRITICAL: Fixed Exchange 2016 OPATH server-side filtering limitations causing domain filter failures
- Enhanced client-side filtering for maximum Exchange 2016 compatibility and reliability
- Improved consistency between mailbox counting and filtering functions
- Added comprehensive debugging output for troubleshooting domain filtering issues
- Replaced problematic server-side filtering with reliable client-side approach

Version 1.7.0 - September 15, 2025 - Configurable Audit Sampling
- Added configurable AuditSampleSize parameter for flexible audit scope control
- Implemented unlimited sampling as default behavior (when parameter omitted)
- Replaced hardcoded 50-record limit with adaptive sampling logic
- Enhanced parameter documentation with performance guidance
- Maintained full backward compatibility with existing audit processes

================================================================================

Current Version: 1.7.0 (Exchange 2016/2019/Online Compatible - Configurable Sampling)
Created: August 17, 2025
Last Updated: September 15, 2025

INSTRUCTIONS FOR ADMIN:
1. Copy this file to the Exchange Server or management workstation
2. Run PowerShell as Administrator and execute:

   # Audit all domains (default behavior)
   .\Exchange-Mailbox-Security-Audit.ps1

   # Audit specific domain
   .\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "domain.com"

   # Audit multiple domains
   .\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter @("Xyyyya.xx", "home.hm")

   # With custom output location
   .\Exchange-Mailbox-Security-Audit.ps1 -OutputPath "C:\Temp\Mailbox-Security-Results.json" -DomainFilter "domain.com"

CRITICAL: This script is 100% READ-ONLY and safe for production environments
All operations use only Get-* cmdlets and read-only Exchange PowerShell commands
================================================================================
#>

[CmdletBinding(DefaultParameterSetName = 'Audit')]
param(
    [Parameter(Mandatory = $false, ParameterSetName = 'Help', Position = 0)]
    [Alias('h')]
    [switch]$Help,

    [Parameter(Mandatory = $false, ParameterSetName = 'Audit')]
    [string]$OutputPath = ".\Exchange-Mailbox-Security-Results.json",

    [Parameter(Mandatory = $false, ParameterSetName = 'Audit')]
    [int]$MaxMailboxSample = 500,

    [Parameter(Mandatory = $false, ParameterSetName = 'Audit')]
    [int]$AuditSampleSize,

    [Parameter(Mandatory = $false, ParameterSetName = 'Audit')]
    [string[]]$DomainFilter = @()
)

# ================================================================================
# POWERSHELL VERSION COMPATIBILITY CHECK
# ================================================================================
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "[ERROR] This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or run on a compatible system." -ForegroundColor Red
    exit 1
}

if ($PSVersionTable.PSVersion.Major -eq 5 -and $PSVersionTable.PSVersion.Minor -eq 0) {
    Write-Host "[WARNING] PowerShell 5.0 detected. PowerShell 5.1 or higher is recommended for optimal performance." -ForegroundColor Yellow
}

Write-Host "[INFO] PowerShell Version: $($PSVersionTable.PSVersion) - Compatible" -ForegroundColor Green

# Validate MaxMailboxSample parameter
if ($MaxMailboxSample -lt 1) {
    Write-Host "[WARNING] Invalid MaxMailboxSample value: $MaxMailboxSample. Using default of 500." -ForegroundColor Yellow
    $MaxMailboxSample = 500
} elseif ($MaxMailboxSample -gt 50000) {
    Write-Host "[WARNING] Very large MaxMailboxSample value: $MaxMailboxSample. This may cause performance issues." -ForegroundColor Yellow
    Write-Host "         Consider using a smaller sample size for better performance." -ForegroundColor Yellow
}

# ================================================================================
# HELP PARAMETER PROCESSING
# ================================================================================
if ($Help) {
    Write-Host "=================================================================================" -ForegroundColor Green
    Write-Host "EXCHANGE MAILBOX SECURITY AUDIT SCRIPT - HELP" -ForegroundColor Green
    Write-Host "=================================================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "SCRIPT INFORMATION:" -ForegroundColor Green
    Write-Host "  Name: Exchange-Mailbox-Security-Audit-v1.7.0.ps1" -ForegroundColor White
    Write-Host "  Version: 1.7.0 (September 15, 2025)" -ForegroundColor White
    Write-Host "  Purpose: Comprehensive Exchange mailbox security controls audit" -ForegroundColor White
    Write-Host "  Author: E.Z. Consultancy - Internal Audit Division" -ForegroundColor White
    Write-Host ""

    Write-Host "DESCRIPTION:" -ForegroundColor Green
    Write-Host "  Performs comprehensive security assessment of Exchange mailbox permissions," -ForegroundColor White
    Write-Host "  impersonation rights, audit logging, and cross-domain relationships." -ForegroundColor White
    Write-Host "  Generates detailed JSON output for compliance analysis and reporting." -ForegroundColor White
    Write-Host ""

    Write-Host "PARAMETERS:" -ForegroundColor Green
    Write-Host "  -Help, -h" -ForegroundColor Cyan
    Write-Host "    Displays this help information and exits." -ForegroundColor White
    Write-Host ""
    Write-Host "  -OutputPath <string>" -ForegroundColor Cyan
    Write-Host "    Specifies the output JSON file path." -ForegroundColor White
    Write-Host "    Default: .\Exchange-Mailbox-Security-Results.json" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  -MaxMailboxSample <int>" -ForegroundColor Cyan
    Write-Host "    Maximum number of mailboxes to retrieve for permission analysis." -ForegroundColor White
    Write-Host "    Default: 500 | Range: 1-50000" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  -AuditSampleSize <int>" -ForegroundColor Cyan
    Write-Host "    Optional parameter to limit audit configuration analysis." -ForegroundColor White
    Write-Host "    When omitted: Analyzes ALL available mailboxes (unlimited sampling)" -ForegroundColor White
    Write-Host "    When specified: Limits audit analysis to specified number of mailboxes" -ForegroundColor White
    Write-Host "    Range: 1-10000 | Performance Note: Values >1000 may impact performance" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  -DomainFilter <string[]>" -ForegroundColor Cyan
    Write-Host "    Optional array of domain names to filter mailboxes." -ForegroundColor White
    Write-Host "    When specified: Only audits mailboxes from specified domains" -ForegroundColor White
    Write-Host "    Format: Single domain or array @('domain1.com', 'domain2.com')" -ForegroundColor Gray
    Write-Host ""

    Write-Host "AUDIT SAMPLING MODES:" -ForegroundColor Green
    Write-Host "  Unlimited Sampling (Default):" -ForegroundColor Cyan
    Write-Host "    - Analyzes ALL available mailboxes for comprehensive coverage" -ForegroundColor White
    Write-Host "    - Recommended for complete security assessment" -ForegroundColor White
    Write-Host "    - May take longer in very large environments (>5000 mailboxes)" -ForegroundColor White
    Write-Host ""
    Write-Host "  Limited Sampling:" -ForegroundColor Cyan
    Write-Host "    - Analyzes specified number of mailboxes for performance optimization" -ForegroundColor White
    Write-Host "    - Useful for large environments or quick assessments" -ForegroundColor White
    Write-Host "    - May miss security issues in unsampled mailboxes" -ForegroundColor White
    Write-Host ""

    Write-Host "USAGE EXAMPLES:" -ForegroundColor Green
    Write-Host "  Basic audit (unlimited sampling):" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "  Performance-optimized audit (limited sampling):" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -AuditSampleSize 200" -ForegroundColor White
    Write-Host ""
    Write-Host "  Single domain audit:" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -DomainFilter 'company.com'" -ForegroundColor White
    Write-Host ""
    Write-Host "  Multiple domain audit with limited sampling:" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -DomainFilter @('domain1.com','domain2.com') -AuditSampleSize 100" -ForegroundColor White
    Write-Host ""
    Write-Host "  Custom output location:" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -OutputPath 'C:\Audits\Exchange-Results.json'" -ForegroundColor White
    Write-Host ""
    Write-Host "  Large environment optimization:" -ForegroundColor Yellow
    Write-Host "    .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -MaxMailboxSample 1000 -AuditSampleSize 300" -ForegroundColor White
    Write-Host ""

    Write-Host "EXCHANGE COMPATIBILITY:" -ForegroundColor Green
    Write-Host "  - Exchange Server 2016 (all CUs)" -ForegroundColor White
    Write-Host "  - Exchange Server 2019 (all CUs)" -ForegroundColor White
    Write-Host "  - Exchange Online (Microsoft 365)" -ForegroundColor White
    Write-Host "  - Hybrid Exchange environments" -ForegroundColor White
    Write-Host ""

    Write-Host "PREREQUISITES:" -ForegroundColor Green
    Write-Host "  PowerShell Requirements:" -ForegroundColor Cyan
    Write-Host "    - PowerShell 5.1 or later (PowerShell 7.x supported)" -ForegroundColor White
    Write-Host "    - Exchange Management Shell or Exchange Online PowerShell module" -ForegroundColor White
    Write-Host ""
    Write-Host "  Required Permissions:" -ForegroundColor Cyan
    Write-Host "    - View-Only Organization Management (minimum)" -ForegroundColor White
    Write-Host "    - Organization Management (recommended for comprehensive assessment)" -ForegroundColor White
    Write-Host "    - Read access to Active Directory (for cross-domain analysis)" -ForegroundColor White
    Write-Host ""
    Write-Host "  Network Requirements:" -ForegroundColor Cyan
    Write-Host "    - Connectivity to Exchange servers or Exchange Online" -ForegroundColor White
    Write-Host "    - Active Directory connectivity (for on-premises environments)" -ForegroundColor White
    Write-Host ""

    Write-Host "PERFORMANCE GUIDANCE:" -ForegroundColor Green
    Write-Host "  Small Environments (<500 mailboxes):" -ForegroundColor Cyan
    Write-Host "    - Use unlimited sampling for complete coverage" -ForegroundColor White
    Write-Host "    - Typical execution time: 2-5 minutes" -ForegroundColor White
    Write-Host ""
    Write-Host "  Medium Environments (500-2000 mailboxes):" -ForegroundColor Cyan
    Write-Host "    - Unlimited sampling recommended, monitor performance" -ForegroundColor White
    Write-Host "    - Consider -AuditSampleSize 500 for faster execution" -ForegroundColor White
    Write-Host "    - Typical execution time: 5-15 minutes" -ForegroundColor White
    Write-Host ""
    Write-Host "  Large Environments (>2000 mailboxes):" -ForegroundColor Cyan
    Write-Host "    - Use -AuditSampleSize parameter for performance optimization" -ForegroundColor White
    Write-Host "    - Recommended: -MaxMailboxSample 1000 -AuditSampleSize 300" -ForegroundColor White
    Write-Host "    - Consider domain filtering for targeted assessments" -ForegroundColor White
    Write-Host "    - Typical execution time: 10-30 minutes" -ForegroundColor White
    Write-Host ""

    Write-Host "SECURITY CONTROLS ASSESSED:" -ForegroundColor Green
    Write-Host "  1. MBX-1.1: Mailbox Impersonation Rights (ApplicationImpersonation role)" -ForegroundColor White
    Write-Host "  2. MBX-2.1: Full Access Permissions (mailbox delegations)" -ForegroundColor White
    Write-Host "  3. MBX-3.1: Audit Logging Configuration (admin and mailbox audit)" -ForegroundColor White
    Write-Host "  4. MBX-4.1: Send-As Permissions (Send-As rights)" -ForegroundColor White
    Write-Host "  5. MBX-5.1: Send-On-Behalf Permissions (Send-On-Behalf delegations)" -ForegroundColor White
    Write-Host "  + Cross-Domain Administrator Discovery and Risk Analysis" -ForegroundColor White
    Write-Host ""

    Write-Host "OUTPUT INFORMATION:" -ForegroundColor Green
    Write-Host "  Format: JSON (JavaScript Object Notation)" -ForegroundColor White
    Write-Host "  Content: Detailed audit findings, compliance scores, recommendations" -ForegroundColor White
    Write-Host "  Usage: Import into compliance analysis tools or convert to reports" -ForegroundColor White
    Write-Host "  Companion: Use jsn-to-txt-v2.ps1 for human-readable text reports" -ForegroundColor White
    Write-Host ""

    Write-Host "=================================================================================" -ForegroundColor Green
    Write-Host "E.Z. CONSULTANCY - INTERNAL AUDIT DIVISION" -ForegroundColor Green
    Write-Host "Exchange Mailbox Security Audit Script v1.7.0" -ForegroundColor Green
    Write-Host "Configurable Sampling | Cross-Domain Analysis | Compliance Reporting" -ForegroundColor Green
    Write-Host "=================================================================================" -ForegroundColor Green

    exit 0
}

# Validate AuditSampleSize parameter
if ($PSBoundParameters.ContainsKey('AuditSampleSize')) {
    if ($AuditSampleSize -lt 1) {
        Write-Host "[WARNING] Invalid AuditSampleSize value: $AuditSampleSize. Using unlimited sampling instead." -ForegroundColor Yellow
        $AuditSampleSize = $null
    } elseif ($AuditSampleSize -gt 10000) {
        Write-Host "[WARNING] Very large AuditSampleSize value: $AuditSampleSize. This may cause performance issues." -ForegroundColor Yellow
        Write-Host "         Consider using a smaller audit sample size for better performance." -ForegroundColor Yellow
    }
    Write-Host "[INFO] Audit Sample Size: Limited to $AuditSampleSize records" -ForegroundColor Green
} else {
    Write-Host "[INFO] Audit Sample Size: Unlimited (all available records will be analyzed)" -ForegroundColor Green
}

# Initialize audit session
$AuditStartTime = Get-Date
$AuditID = [System.Guid]::NewGuid()
$AuditResults = @{}
$SampleMailboxes = @()  # Initialize as empty array to prevent null reference errors

# Constants for thresholds and intervals
$PROGRESS_REPORT_INTERVAL = 50
$FULL_ACCESS_THRESHOLD = 0.05
$SENDAS_THRESHOLD = 0.03
$SENDONBEHALF_THRESHOLD = 0.05

# Domain filtering configuration with validation
if ($DomainFilter) {
    $ValidatedDomains = @()
    foreach ($domain in $DomainFilter) {
        if (-not [string]::IsNullOrWhiteSpace($domain) -and $domain -match '^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$') {
            $ValidatedDomains += $domain.ToLower().Trim()
        } else {
            Write-Host "[WARNING] Invalid domain format: '$domain'. Skipping." -ForegroundColor Yellow
        }
    }

    if ($ValidatedDomains.Count -eq 0) {
        Write-Host "[WARNING] No valid domains in filter. Proceeding without domain filtering." -ForegroundColor Yellow
        $DomainFilter = @()
    } else {
        $DomainFilter = $ValidatedDomains
    }
}

$IsDomainFilterEnabled = $DomainFilter -and $DomainFilter.Count -gt 0
$FilteredDomains = if ($IsDomainFilterEnabled) { $DomainFilter | ForEach-Object { $_.ToLower().Trim() } } else { @() }

# Additional validation: Check for reasonable domain filter size
if ($IsDomainFilterEnabled -and $FilteredDomains.Count -gt 50) {
    Write-Host "[WARNING] Large number of domains in filter ($($FilteredDomains.Count)). This may impact performance." -ForegroundColor Yellow
    Write-Host "         Consider reducing the number of domains or running separate audits for optimal performance." -ForegroundColor Yellow
}

# Safe domain extraction functions
function Get-SafeDomainFromEmail {
    param(
        [string]$EmailAddress,
        [string]$DefaultValue = "Unknown"
    )

    if ([string]::IsNullOrWhiteSpace($EmailAddress)) {
        return $DefaultValue
    }

    try {
        $emailParts = $EmailAddress -split '@'
        if ($emailParts.Count -ge 2 -and -not [string]::IsNullOrWhiteSpace($emailParts[1])) {
            return $emailParts[1].ToLower().Trim()
        } else {
            return $DefaultValue
        }
    } catch {
        return $DefaultValue
    }
}

function Get-SafeDomainFromAccount {
    param(
        [string]$AccountName,
        [string]$DefaultValue = "Unknown"
    )

    if ([string]::IsNullOrWhiteSpace($AccountName)) {
        return $DefaultValue
    }

    try {
        # Check for email format first
        if ($AccountName -like '*@*') {
            return Get-SafeDomainFromEmail -EmailAddress $AccountName -DefaultValue $DefaultValue
        }

        # Check for domain\username format
        if ($AccountName -like '*\*') {
            $accountParts = $AccountName -split '\\'
            if ($accountParts.Count -ge 1 -and -not [string]::IsNullOrWhiteSpace($accountParts[0])) {
                return $accountParts[0].ToLower().Trim()
            }
        }

        return $DefaultValue
    } catch {
        return $DefaultValue
    }
}

# Helper function to check if an email address matches the domain filter
function Test-EmailDomainMatch {
    param(
        [string]$EmailAddress
    )

    if ($null -eq $EmailAddress) {
        return $false
    }

    if (-not $IsDomainFilterEnabled) {
        return $true  # No filter applied, include all
    }

    if ([string]::IsNullOrWhiteSpace($EmailAddress)) {
        return $false  # Invalid email, exclude
    }

    try {
        $Domain = Get-SafeDomainFromEmail -EmailAddress $EmailAddress -DefaultValue ""
        if ([string]::IsNullOrWhiteSpace($Domain)) {
            return $false  # Invalid email format, exclude
        }

        # Check if domain matches any of the filtered domains
        return $FilteredDomains -contains $Domain
    }
    catch {
        Write-Host "  [WARNING] Failed to parse email address: $EmailAddress" -ForegroundColor Yellow
        return $false  # Error parsing, exclude for safety
    }
}

# Helper function to convert complex objects to JSON-serializable format
function ConvertTo-SerializableObject {
    param(
        [Parameter(Mandatory = $false)]
        $InputObject
    )

    if ($null -eq $InputObject) {
        return $null
    }

    # Handle different object types
    switch ($InputObject.GetType().Name) {
        "Hashtable" {
            $SerializableHash = @{}
            foreach ($key in $InputObject.Keys) {
                $SerializableHash[$key.ToString()] = ConvertTo-SerializableObject -InputObject $InputObject[$key]
            }
            return $SerializableHash
        }
        "ArrayList" {
            $SerializableArray = @()
            foreach ($item in $InputObject) {
                $SerializableArray += ConvertTo-SerializableObject -InputObject $item
            }
            return $SerializableArray
        }
        "Object[]" {
            $SerializableArray = @()
            foreach ($item in $InputObject) {
                $SerializableArray += ConvertTo-SerializableObject -InputObject $item
            }
            return $SerializableArray
        }
        default {
            # Handle different object types safely
            try {
                # For complex Exchange objects, convert to string representation
                $typeName = $InputObject.GetType().FullName
                if ($typeName -like "*Microsoft.Exchange.Data.SmtpAddress*" -or
                    $typeName -like "*Microsoft.Exchange.Data.Directory.ADObjectId*" -or
                    $typeName -like "*Microsoft.Exchange*") {
                    return $InputObject.ToString()
                }
                elseif ($InputObject -is [System.DateTime]) {
                    return $InputObject.ToString("yyyy-MM-dd HH:mm:ss")
                }
                elseif ($InputObject -is [System.TimeSpan]) {
                    return $InputObject.ToString()
                }
                elseif ($InputObject -is [System.Guid]) {
                    return $InputObject.ToString()
                }
                elseif ($InputObject.GetType().IsEnum) {
                    return $InputObject.ToString()
                }
                elseif ($InputObject -is [System.Collections.IEnumerable] -and $InputObject -isnot [string]) {
                    $SerializableArray = @()
                    foreach ($item in $InputObject) {
                        $SerializableArray += ConvertTo-SerializableObject -InputObject $item
                    }
                    return $SerializableArray
                }
                else {
                    # For other complex objects, try to convert to string or return as-is for primitives
                    if ($InputObject.GetType().IsPrimitive -or $InputObject -is [string] -or $InputObject -is [decimal]) {
                        return $InputObject
                    }
                    else {
                        return $InputObject.ToString()
                    }
                }
            }
            catch {
                # If type checking fails, safely convert to string
                try {
                    return $InputObject.ToString()
                }
                catch {
                    return "Unable to serialize object"
                }
            }
        }
    }
}

# Helper function to filter mailboxes by domain - Exchange 2016 Compatible Version
function Get-FilteredMailboxes {
    param(
        [Parameter(Mandatory = $true)]
        [int]$ResultSize
    )

    if ($ResultSize -le 0) {
        Write-Host "  [WARNING] Invalid ResultSize specified: $ResultSize. Using default of 500." -ForegroundColor Yellow
        $ResultSize = 500
    }

    Write-Host "  [DEBUG] Get-FilteredMailboxes called with ResultSize: $ResultSize" -ForegroundColor Gray
    Write-Host "  [DEBUG] Domain filter enabled: $IsDomainFilterEnabled" -ForegroundColor Gray
    if ($IsDomainFilterEnabled) {
        Write-Host "  [DEBUG] Filtered domains: $($FilteredDomains -join ', ')" -ForegroundColor Gray
    }

    try {
        if ($IsDomainFilterEnabled) {
            Write-Host "  Applying domain filter for: $($FilteredDomains -join ', ')" -ForegroundColor Cyan

            # Exchange 2016 Compatibility: Use client-side filtering for maximum reliability
            # Server-side OPATH filtering has known limitations in Exchange 2016
            Write-Host "  [DEBUG] Using client-side filtering for Exchange 2016 compatibility" -ForegroundColor Gray

            $FilteredMailboxes = @()

            try {
                Write-Host "  [DEBUG] Retrieving all mailboxes for client-side filtering..." -ForegroundColor Gray
                $AllMailboxes = Get-Mailbox -ResultSize Unlimited -ErrorAction SilentlyContinue

                if ($AllMailboxes) {
                    # Convert to array if single object - handle different object types safely
                    if ($AllMailboxes.GetType().Name -notlike "*Object*" -and $AllMailboxes.GetType().Name -notlike "*Array*") {
                        $AllMailboxes = @($AllMailboxes)
                    }

                    Write-Host "  [DEBUG] Retrieved $($AllMailboxes.Count) total mailboxes for filtering" -ForegroundColor Gray
                    Write-Host "  [DEBUG] Applying domain filter using Test-EmailDomainMatch function..." -ForegroundColor Gray

                    # Apply domain filtering using the same logic that works in the counting phase
                    $FilteredMailboxes = $AllMailboxes | Where-Object {
                        try {
                            $emailAddress = $_.PrimarySmtpAddress.ToString()
                            $matchResult = Test-EmailDomainMatch -EmailAddress $emailAddress
                            if ($matchResult) {
                                Write-Host "    [DEBUG] Mailbox matched: $emailAddress" -ForegroundColor DarkGray
                            }
                            return $matchResult
                        } catch {
                            Write-Host "    [DEBUG] Error processing mailbox $($_.Identity): $($_.Exception.Message)" -ForegroundColor DarkGray
                            return $false
                        }
                    }

                    # Apply result size limit after filtering to ensure we get the requested number
                    if ($FilteredMailboxes.Count -gt $ResultSize) {
                        Write-Host "  [DEBUG] Limiting results from $($FilteredMailboxes.Count) to $ResultSize mailboxes" -ForegroundColor Gray
                        $FilteredMailboxes = $FilteredMailboxes | Select-Object -First $ResultSize
                    }

                    Write-Host "  [DEBUG] Final filtered mailbox count: $($FilteredMailboxes.Count)" -ForegroundColor Gray
                    Write-Host "  Found $($FilteredMailboxes.Count) mailboxes matching domain filter" -ForegroundColor Cyan

                    # Additional debugging: Show sample of filtered mailboxes
                    if ($FilteredMailboxes.Count -gt 0) {
                        $sampleCount = [Math]::Min(3, $FilteredMailboxes.Count)
                        Write-Host "  [DEBUG] Sample filtered mailboxes:" -ForegroundColor Gray
                        for ($i = 0; $i -lt $sampleCount; $i++) {
                            Write-Host "    [DEBUG] - $($FilteredMailboxes[$i].PrimarySmtpAddress)" -ForegroundColor Gray
                        }
                        if ($FilteredMailboxes.Count -gt 3) {
                            Write-Host "    [DEBUG] ... and $($FilteredMailboxes.Count - 3) more" -ForegroundColor Gray
                        }
                    }
                } else {
                    Write-Host "  [WARNING] No mailboxes retrieved from Get-Mailbox command" -ForegroundColor Yellow
                    Write-Host "  [DEBUG] AllMailboxes is null or empty" -ForegroundColor Gray
                }
            } catch {
                Write-Host "  [ERROR] Client-side filtering failed: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "  [DEBUG] Exception details: $($_.Exception.GetType().FullName)" -ForegroundColor Gray
                Write-Host "  [DEBUG] Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
                return @()
            }

            return $FilteredMailboxes
        }
        else {
            # No domain filter, use original logic
            Write-Host "  [DEBUG] No domain filter applied, retrieving all mailboxes" -ForegroundColor Gray
            $AllMailboxes = Get-Mailbox -ResultSize $ResultSize -ErrorAction Stop

            # Convert to array if single object - handle different object types safely
            if ($AllMailboxes -and $AllMailboxes.GetType().Name -notlike "*Object*" -and $AllMailboxes.GetType().Name -notlike "*Array*") {
                $AllMailboxes = @($AllMailboxes)
            }

            Write-Host "  [DEBUG] Retrieved $($AllMailboxes.Count) mailboxes (no domain filter)" -ForegroundColor Gray
            return $AllMailboxes
        }
    }
    catch {
        Write-Host "  [ERROR] Failed to retrieve mailboxes: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  [DEBUG] Exception details: $($_.Exception.GetType().FullName)" -ForegroundColor Gray
        Write-Host "  [DEBUG] Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
        return @()
    }
}

# Enhanced function to discover all Exchange administrators (cross-domain)
function Get-ExchangeAdministratorDiscovery {
    Write-Host "  Performing administrator discovery across all domains..." -ForegroundColor Cyan

    $AdminDiscovery = @{
        RoleAssignments = @()
        AdminDomainMap = @{}
        CrossDomainRelationships = @()
        AdminClassification = @{}
    }

    try {
        # Get all management role assignments
        $AllRoleAssignments = Get-ManagementRoleAssignment -ErrorAction SilentlyContinue

        foreach ($assignment in $AllRoleAssignments) {
            if (-not $assignment.RoleAssignee) { continue }

            # Determine assignee domain
            $assigneeDomain = "Unknown"
            $assigneeType = "Unknown"

            if ($assignment.RoleAssignee -like "*@*") {
                $assigneeDomain = Get-SafeDomainFromEmail -EmailAddress $assignment.RoleAssignee -DefaultValue "Unknown"
                $assigneeType = "User"
            } elseif ($assignment.RoleAssignee -like "*\*") {
                $assigneeDomain = Get-SafeDomainFromAccount -AccountName $assignment.RoleAssignee -DefaultValue "Unknown"
                $assigneeType = "User"
            } elseif ($assignment.RoleAssigneeType -eq "RoleGroup") {
                $assigneeType = "RoleGroup"
                # Try to get domain from role group members
                try {
                    $roleGroup = Get-RoleGroup -Identity $assignment.RoleAssignee -ErrorAction SilentlyContinue
                    if ($roleGroup -and $roleGroup.Members -and $roleGroup.Members.Count -gt 0) {
                        try {
                            $firstMember = $roleGroup.Members[0]
                            if ($firstMember) {
                                $firstMemberString = $firstMember.ToString()
                                $assigneeDomain = Get-SafeDomainFromAccount -AccountName $firstMemberString -DefaultValue "Unknown"
                            }
                        } catch {
                            # Continue with Unknown domain if member processing fails
                            Write-Host "    [DEBUG] Could not process role group member for domain determination" -ForegroundColor Gray
                        }
                    }
                } catch {
                    # Continue with Unknown domain
                }
            }

            # Analyze scope and affected domains
            $scopeAnalysis = @{
                Type = "Unknown"
                AffectedDomains = @()
                IsOrganizationWide = $false
            }

            if ($assignment.RecipientWriteScope -eq "Organization") {
                $scopeAnalysis.Type = "Organization-Wide"
                $scopeAnalysis.IsOrganizationWide = $true
                $scopeAnalysis.AffectedDomains = @("All")
            } elseif ($assignment.CustomRecipientWriteScope) {
                $scopeAnalysis.Type = "Custom-Scope"
                try {
                    $customScope = Get-ManagementScope -Identity $assignment.CustomRecipientWriteScope -ErrorAction SilentlyContinue
                    if ($customScope -and $customScope.RecipientFilter) {
                        # Parse filter to extract domain information
                        if ($customScope.RecipientFilter -match "@(\w+\.\w+)") {
                            $scopeAnalysis.AffectedDomains += $matches[1].ToLower()
                        }
                    }
                } catch {
                    # Continue with unknown scope
                }
            } elseif ($assignment.RecipientOrganizationalUnitScope) {
                $scopeAnalysis.Type = "OU-Scope"
                $scopeAnalysis.AffectedDomains = @("OU-Based")
            }

            # Classify administrator type
            $adminClassification = "Business-User"
            if ($assignment.Role -like "*Organization Management*" -or
                $assignment.Role -like "*Exchange*Admin*" -or
                $assignment.Role -like "*Server Management*") {
                $adminClassification = "Exchange-Administrator"
            } elseif ($assignment.Role -like "*Recipient Management*" -or
                      $assignment.Role -like "*Mail Recipients*") {
                $adminClassification = "Mailbox-Administrator"
            } elseif ($assignment.Role -like "*View-Only*" -or
                      $assignment.Role -like "*Monitoring*") {
                $adminClassification = "Read-Only-Administrator"
            }

            $assignmentInfo = @{
                RoleAssignee = $assignment.RoleAssignee.ToString()
                AssigneeDomain = $assigneeDomain
                AssigneeType = $assigneeType
                Role = $assignment.Role.ToString()
                RoleType = $assignment.RoleType.ToString()
                Scope = $scopeAnalysis
                IsEnabled = $assignment.Enabled
                AdminClassification = $adminClassification
                AssignmentMethod = $assignment.AssignmentMethod.ToString()
            }

            $AdminDiscovery.RoleAssignments += $assignmentInfo

            # Build domain map
            if (-not $AdminDiscovery.AdminDomainMap.ContainsKey($assigneeDomain)) {
                $AdminDiscovery.AdminDomainMap[$assigneeDomain] = @()
            }
            $AdminDiscovery.AdminDomainMap[$assigneeDomain] += $assignmentInfo

            # Store admin classification
            $AdminDiscovery.AdminClassification[$assignment.RoleAssignee.ToString()] = $adminClassification
        }

        Write-Host "  Discovered $($AdminDiscovery.RoleAssignments.Count) role assignments across $($AdminDiscovery.AdminDomainMap.Keys.Count) domains" -ForegroundColor Green

        return $AdminDiscovery
    }
    catch {
        Write-Host "  [ERROR] Administrator discovery failed: $($_.Exception.Message)" -ForegroundColor Red
        return $AdminDiscovery
    }
}

# Function to correlate administrators with filtered mailboxes (cross-domain analysis)
function Get-CrossDomainPermissionCorrelation {
    param(
        [Parameter(Mandatory = $true)]
        $AdminDiscovery,
        [Parameter(Mandatory = $true)]
        $FilteredMailboxes
    )

    Write-Host "  Analyzing cross-domain permission relationships..." -ForegroundColor Cyan

    $CorrelationResults = @{
        CrossDomainRelationships = @()
        PermissionSummary = @{}
        RiskAssessment = @{}
        ComplianceGaps = @()
    }

    try {
        foreach ($mailbox in $FilteredMailboxes) {
            $mailboxDomain = Get-SafeDomainFromEmail -EmailAddress $mailbox.PrimarySmtpAddress.ToString() -DefaultValue "Unknown"

            # Get direct permissions for this mailbox
            try {
                $directPermissions = Get-MailboxPermission -Identity $mailbox.Identity -ErrorAction SilentlyContinue |
                    Where-Object {
                        $_.User -notlike "NT AUTHORITY*" -and
                        $_.User -notlike "S-1-*" -and
                        $_.User -ne $mailbox.PrimarySmtpAddress
                    }

                foreach ($permission in $directPermissions) {
                    # Determine administrator domain
                    $adminDomain = "Unknown"
                    if ($permission.User -like "*@*") {
                        $adminDomain = Get-SafeDomainFromEmail -EmailAddress $permission.User.ToString() -DefaultValue "Unknown"
                    } elseif ($permission.User -like "*\*") {
                        $adminDomain = Get-SafeDomainFromAccount -AccountName $permission.User.ToString() -DefaultValue "Unknown"
                    }

                    # Check if this is a cross-domain relationship
                    $isCrossDomain = ($adminDomain -ne $mailboxDomain -and $adminDomain -ne "Unknown")

                    # Get admin classification from discovery
                    $adminClassification = "Unknown"
                    if ($AdminDiscovery.AdminClassification.ContainsKey($permission.User)) {
                        $adminClassification = $AdminDiscovery.AdminClassification[$permission.User]
                    } else {
                        # Try to classify based on permission type
                        if ($permission.AccessRights -contains "FullAccess") {
                            $adminClassification = "Business-User-Delegate"
                        }
                    }

                    # Calculate risk score
                    $riskScore = 0
                    if ($isCrossDomain) { $riskScore += 3 }
                    if ($permission.AccessRights -contains "FullAccess") { $riskScore += 2 }
                    if ($adminClassification -eq "Exchange-Administrator") { $riskScore += 1 }
                    if ($permission.IsInherited -eq $false) { $riskScore += 1 }

                    $riskLevel = "Low"
                    if ($riskScore -ge 5) { $riskLevel = "High" }
                    elseif ($riskScore -ge 3) { $riskLevel = "Medium" }

                    $relationshipInfo = @{
                        MailboxIdentity = $mailbox.Identity.ToString()
                        MailboxDisplayName = $mailbox.DisplayName.ToString()
                        MailboxPrimarySmtpAddress = $mailbox.PrimarySmtpAddress.ToString()
                        MailboxDomain = $mailboxDomain
                        AdminUser = $permission.User.ToString()
                        AdminDomain = $adminDomain
                        AdminClassification = $adminClassification
                        AccessRights = ($permission.AccessRights | ForEach-Object { $_.ToString() }) -join ", "
                        IsInherited = $permission.IsInherited
                        IsCrossDomain = $isCrossDomain
                        RiskScore = $riskScore
                        RiskLevel = $riskLevel
                        PermissionType = "Direct"
                    }

                    $CorrelationResults.CrossDomainRelationships += $relationshipInfo

                    # Track cross-domain relationships for summary
                    if ($isCrossDomain) {
                        $relationshipKey = "$adminDomain -> $mailboxDomain"
                        if (-not $CorrelationResults.PermissionSummary.ContainsKey($relationshipKey)) {
                            $CorrelationResults.PermissionSummary[$relationshipKey] = @{
                                Count = 0
                                AdminDomain = $adminDomain
                                MailboxDomain = $mailboxDomain
                                HighRiskCount = 0
                                AdminTypes = @{}
                            }
                        }
                        $CorrelationResults.PermissionSummary[$relationshipKey].Count++
                        if ($riskLevel -eq "High") {
                            $CorrelationResults.PermissionSummary[$relationshipKey].HighRiskCount++
                        }
                        if (-not $CorrelationResults.PermissionSummary[$relationshipKey].AdminTypes.ContainsKey($adminClassification)) {
                            $CorrelationResults.PermissionSummary[$relationshipKey].AdminTypes[$adminClassification] = 0
                        }
                        $CorrelationResults.PermissionSummary[$relationshipKey].AdminTypes[$adminClassification]++
                    }
                }
            }
            catch {
                Write-Host "    [WARNING] Failed to get permissions for mailbox $($mailbox.Identity): $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        # Generate risk assessment summary
        $totalRelationships = $CorrelationResults.CrossDomainRelationships.Count
        $crossDomainCount = ($CorrelationResults.CrossDomainRelationships | Where-Object { $_.IsCrossDomain }).Count
        $highRiskCount = ($CorrelationResults.CrossDomainRelationships | Where-Object { $_.RiskLevel -eq "High" }).Count

        $CorrelationResults.RiskAssessment = @{
            TotalPermissionRelationships = $totalRelationships
            CrossDomainRelationships = $crossDomainCount
            HighRiskRelationships = $highRiskCount
            CrossDomainPercentage = if ($totalRelationships -gt 0) { [math]::Round(($crossDomainCount / $totalRelationships) * 100, 2) } else { 0 }
            RiskDistribution = @{
                High = ($CorrelationResults.CrossDomainRelationships | Where-Object { $_.RiskLevel -eq "High" }).Count
                Medium = ($CorrelationResults.CrossDomainRelationships | Where-Object { $_.RiskLevel -eq "Medium" }).Count
                Low = ($CorrelationResults.CrossDomainRelationships | Where-Object { $_.RiskLevel -eq "Low" }).Count
            }
        }

        Write-Host "  Found $crossDomainCount cross-domain relationships out of $totalRelationships total ($($CorrelationResults.RiskAssessment.CrossDomainPercentage)%)" -ForegroundColor Green

        return $CorrelationResults
    }
    catch {
        Write-Host "  [ERROR] Cross-domain correlation analysis failed: $($_.Exception.Message)" -ForegroundColor Red
        return $CorrelationResults
    }
}

# ================================================================================
# SCRIPT VERSION AND ENVIRONMENT INFORMATION
# ================================================================================
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Server Mailbox Security Audit Script" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Script Version: 1.7.0 (September 15, 2025)" -ForegroundColor White
Write-Host "Created By: E.Z. Consultancy" -ForegroundColor White
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "PowerShell Edition: $($PSVersionTable.PSEdition)" -ForegroundColor White
Write-Host "Execution Host: $($env:COMPUTERNAME)" -ForegroundColor White
Write-Host "Execution User: $($env:USERNAME)" -ForegroundColor White
Write-Host "Execution Time: $($AuditStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor White
if ($IsDomainFilterEnabled) {
    Write-Host "Domain Filter: $($FilteredDomains -join ', ')" -ForegroundColor Cyan
} else {
    Write-Host "Domain Filter: None (All domains)" -ForegroundColor Cyan
}
Write-Host "Max Sample Size: $MaxMailboxSample mailboxes" -ForegroundColor Cyan
if ($PSBoundParameters.ContainsKey('AuditSampleSize') -and $AuditSampleSize -gt 0) {
    Write-Host "Audit Sample Size: Limited to $AuditSampleSize records" -ForegroundColor Cyan
} else {
    Write-Host "Audit Sample Size: Unlimited (comprehensive analysis)" -ForegroundColor Cyan
}
Write-Host "Output Location: $OutputPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit ID: $AuditID" -ForegroundColor Yellow
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

try {
    # =============================================================================
    # AUDIT METADATA AND ENVIRONMENT INFORMATION
    # =============================================================================
    
    Write-Host "Step 1/7: Collecting Exchange environment metadata..." -ForegroundColor Yellow
    
    try {
        $OrganizationConfig = Get-OrganizationConfig -ErrorAction Stop
        $ExchangeServers = Get-ExchangeServer -ErrorAction SilentlyContinue
        # Get total mailboxes count (filtered if domain filter is applied)
        if ($IsDomainFilterEnabled) {
            # Exchange 2016 Compatibility: Use client-side counting for consistency with filtering
            Write-Host "  [DEBUG] Starting domain-filtered mailbox counting..." -ForegroundColor Gray
            $TotalMailboxes = 0
            try {
                # Use client-side counting for consistency with Get-FilteredMailboxes function
                Write-Host "  [DEBUG] Using client-side counting for Exchange 2016 compatibility" -ForegroundColor Gray
                $AllMailboxes = Get-Mailbox -ResultSize Unlimited -ErrorAction SilentlyContinue

                if ($AllMailboxes) {
                    Write-Host "  [DEBUG] Retrieved $($AllMailboxes.Count) total mailboxes for counting" -ForegroundColor Gray

                    $FilteredMailboxCount = ($AllMailboxes | Where-Object {
                        try {
                            $emailAddress = $_.PrimarySmtpAddress.ToString()
                            $matchResult = Test-EmailDomainMatch -EmailAddress $emailAddress
                            return $matchResult
                        } catch {
                            Write-Host "    [DEBUG] Error processing mailbox during counting: $($_.Exception.Message)" -ForegroundColor DarkGray
                            return $false
                        }
                    } | Measure-Object).Count

                    $TotalMailboxes = $FilteredMailboxCount
                    Write-Host "  [DEBUG] Client-side counting completed: $TotalMailboxes mailboxes match filter" -ForegroundColor Gray
                } else {
                    Write-Host "  [WARNING] No mailboxes retrieved for counting" -ForegroundColor Yellow
                    $TotalMailboxes = 0
                }

                Write-Host "  Domain filter applied: $TotalMailboxes mailboxes match filter" -ForegroundColor Cyan

                # Additional debugging for domain breakdown
                if ($TotalMailboxes -gt 0) {
                    Write-Host "  [DEBUG] Domain breakdown:" -ForegroundColor Gray
                    foreach ($domain in $FilteredDomains) {
                        $domainSpecificCount = ($AllMailboxes | Where-Object {
                            try {
                                $emailDomain = Get-SafeDomainFromEmail -EmailAddress $_.PrimarySmtpAddress.ToString() -DefaultValue ""
                                return $emailDomain -eq $domain.ToLower()
                            } catch {
                                return $false
                            }
                        } | Measure-Object).Count
                        Write-Host "    [DEBUG] Domain $domain : $domainSpecificCount mailboxes" -ForegroundColor Gray
                    }
                }
            } catch {
                Write-Host "  [ERROR] Could not count filtered mailboxes: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "  [DEBUG] Exception details: $($_.Exception.GetType().FullName)" -ForegroundColor Gray
                $TotalMailboxes = "Unknown"
            }
        }
        else {
            $TotalMailboxes = (Get-Mailbox -ResultSize Unlimited -ErrorAction SilentlyContinue | Measure-Object).Count
        }
        
        $AuditMetadata = @{}
        $AuditMetadata.AuditID = $AuditID
        $AuditMetadata.AuditStartTime = $AuditStartTime
        $AuditMetadata.ScriptVersion = "1.7.0"
        $AuditMetadata.ScriptReleaseDate = "September 15, 2025"
        $AuditMetadata.ScriptAuthor = "E.Z. Consultancy"
        $AuditMetadata.PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        $AuditMetadata.PowerShellEdition = $PSVersionTable.PSEdition
        $AuditMetadata.OrganizationName = $OrganizationConfig.Name
        $AuditMetadata.ExchangeServers = if ($ExchangeServers) { $ExchangeServers.Count } else { "Unknown" }
        $AuditMetadata.TotalMailboxes = $TotalMailboxes
        $AuditMetadata.MaxMailboxSample = $MaxMailboxSample
        $AuditMetadata.AssessmentScope = "5 Critical Mailbox Security Controls"
        $AuditMetadata.AuditUser = $env:USERNAME
        $AuditMetadata.ComputerName = $env:COMPUTERNAME
        $AuditMetadata.DomainFilterEnabled = $IsDomainFilterEnabled
        $AuditMetadata.FilteredDomains = if ($IsDomainFilterEnabled) { $FilteredDomains } else { @() }
        $AuditMetadata.DomainFilterScope = if ($IsDomainFilterEnabled) { "Domain-specific audit for: $($FilteredDomains -join ', ')" } else { "All domains (no filter applied)" }
        
        $AuditResults.Add("AuditMetadata", $AuditMetadata)
        Write-Host "[SUCCESS] Environment metadata collected successfully" -ForegroundColor Green
        Write-Host "  Metadata sections: $($AuditMetadata.Keys.Count)" -ForegroundColor Gray
    }
    catch {
        Write-Host "[ERROR] Failed to collect environment metadata: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AuditMetadata", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # ADMINISTRATOR DISCOVERY (CROSS-DOMAIN ANALYSIS)
    # =============================================================================

    Write-Host "Step 2/7: Performing administrator discovery..." -ForegroundColor Yellow

    try {
        $AdminDiscovery = Get-ExchangeAdministratorDiscovery
        $AuditResults.Add("AdministratorDiscovery", $AdminDiscovery)
        Write-Host "[SUCCESS] Administrator discovery completed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Administrator discovery failed: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AdministratorDiscovery", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # PRE-FLIGHT VALIDATION
    # =============================================================================

    Write-Host "Performing pre-flight validation..." -ForegroundColor Yellow

    # Check if Exchange cmdlets are available
    try {
        Get-Command Get-Mailbox -ErrorAction Stop | Out-Null
        Write-Host "[SUCCESS] Exchange PowerShell module is available" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Exchange PowerShell module not found. Please run from Exchange Management Shell." -ForegroundColor Red
        throw "Exchange PowerShell module required"
    }

    # Check Exchange connectivity
    try {
        $null = Get-OrganizationConfig -ErrorAction Stop
        Write-Host "[SUCCESS] Exchange connectivity verified" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Cannot connect to Exchange: $($_.Exception.Message)" -ForegroundColor Red
        throw "Exchange connectivity required"
    }

    # =============================================================================
    # CONTROL 1: MAILBOX IMPERSONATION RIGHTS
    # =============================================================================
    
    Write-Host "Step 3/7: Assessing mailbox impersonation rights..." -ForegroundColor Yellow
    
    try {
        $ImpersonationRoleAssignments = Get-ManagementRoleAssignment -Role "ApplicationImpersonation" -ErrorAction SilentlyContinue
        $ImpersonationUsers = @()

        foreach ($Assignment in $ImpersonationRoleAssignments) {
            $ImpersonationUsers += @{
                RoleAssignee = if ($Assignment) { $Assignment.RoleAssignee.ToString() } else { "Unknown" }
                RoleAssigneeName = if ($Assignment) { $Assignment.RoleAssigneeName.ToString() } else { "Unknown" }
                RoleAssigneeType = if ($Assignment) { $Assignment.RoleAssigneeType.ToString() } else { "Unknown" }
                AssignmentMethod = if ($Assignment) { $Assignment.AssignmentMethod.ToString() } else { "Unknown" }
                IsValid = if ($Assignment) { $Assignment.IsValid } else { $false }
                WhenCreated = if ($Assignment) { $Assignment.WhenCreated.ToString("yyyy-MM-dd HH:mm:ss") } else { "Unknown" }
            }
        }
        
        $ImpersonationControl = @{
            ControlID = "MBX-1.1"
            ControlName = "Mailbox Impersonation Rights"
            RiskLevel = "Critical"
            CVSSScore = 9.3
            TotalImpersonationAssignments = $ImpersonationRoleAssignments.Count
            ImpersonationUsers = $ImpersonationUsers
            CurrentValue = "$($ImpersonationRoleAssignments.Count) accounts with ApplicationImpersonation rights"
            BaselineValue = "Minimal impersonation rights with regular review"
            ComplianceStatus = if ($ImpersonationRoleAssignments.Count -le 2) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($ImpersonationRoleAssignments.Count -eq 0) { 100 } elseif ($ImpersonationRoleAssignments.Count -le 2) { 75 } else { 25 }
            Finding = if ($ImpersonationRoleAssignments.Count -eq 0) { "No impersonation rights assigned - secure configuration" } elseif ($ImpersonationRoleAssignments.Count -le 2) { "Limited impersonation rights - review assignments" } else { "High number of impersonation rights - security risk" }
            Recommendation = "Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions"
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("MBX_1_1_ImpersonationRights", $ImpersonationControl)
        Write-Host "[SUCCESS] Mailbox impersonation rights assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess impersonation rights: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("MBX_1_1_ImpersonationRights", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 2: FULL ACCESS PERMISSIONS
    # =============================================================================
    
    Write-Host "Step 4/7: Assessing mailbox full access permissions..." -ForegroundColor Yellow
    
    try {
        $SampleMailboxes = Get-FilteredMailboxes -ResultSize $MaxMailboxSample
        if (-not $SampleMailboxes) {
            $SampleMailboxes = @()  # Ensure it's never null
            Write-Host "  No mailboxes found matching the criteria" -ForegroundColor Yellow
        }
        $FullAccessPermissions = @()
        $ProcessedCount = 0

        foreach ($Mailbox in $SampleMailboxes) {
            $ProcessedCount++
            if ($ProcessedCount % $PROGRESS_REPORT_INTERVAL -eq 0) {
                Write-Host "  Processed $ProcessedCount of $($SampleMailboxes.Count) mailboxes..." -ForegroundColor Gray
            }
            
            try {
                $Permissions = Get-MailboxPermission -Identity $Mailbox.Identity -ErrorAction SilentlyContinue | 
                    Where-Object { $_.AccessRights -contains "FullAccess" -and $_.User -notlike "NT AUTHORITY\*" -and $_.User -notlike "S-1-*" -and $_.User -ne $Mailbox.PrimarySmtpAddress }
                
                foreach ($Permission in $Permissions) {
                    $FullAccessPermissions += @{
                        MailboxIdentity = if ($Mailbox) { $Mailbox.Identity.ToString() } else { "Unknown" }
                        MailboxDisplayName = if ($Mailbox) { $Mailbox.DisplayName.ToString() } else { "Unknown" }
                        MailboxPrimarySmtpAddress = if ($Mailbox) { $Mailbox.PrimarySmtpAddress.ToString() } else { "Unknown" }
                        User = if ($Permission) { $Permission.User.ToString() } else { "Unknown" }
                        AccessRights = if ($Permission -and $Permission.AccessRights) { ($Permission.AccessRights | ForEach-Object { $_.ToString() }) -join ", " } else { "Unknown" }
                        IsInherited = if ($Permission) { $Permission.IsInherited } else { $false }
                        Deny = if ($Permission) { $Permission.Deny } else { $false }
                    }
                }
            }
            catch {
                # Continue with next mailbox if permission check fails
                continue
            }
        }
        
        # Null-safe calculations
        $SampleSize = if ($SampleMailboxes) { $SampleMailboxes.Count } else { 0 }
        $ComplianceThreshold = if ($SampleSize -gt 0) { $SampleSize * $FULL_ACCESS_THRESHOLD } else { 0 }

        $FullAccessControl = @{
            ControlID = "MBX-2.1"
            ControlName = "Mailbox Full Access Permissions"
            RiskLevel = "High"
            CVSSScore = 8.1
            SampleSize = $SampleSize
            TotalFullAccessPermissions = $FullAccessPermissions.Count
            FullAccessPermissions = $FullAccessPermissions | Select-Object -First 100  # Limit output size
            UniqueUsersWithFullAccess = ($FullAccessPermissions | Select-Object User -Unique).Count
            CurrentValue = "$($FullAccessPermissions.Count) Full Access permissions found across $SampleSize mailboxes"
            BaselineValue = "Minimal Full Access delegations with business justification"
            ComplianceStatus = if ($SampleSize -eq 0) { "No Data" } elseif ($FullAccessPermissions.Count -le $ComplianceThreshold) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($FullAccessPermissions.Count -eq 0) { 100 } elseif ($FullAccessPermissions.Count -le ($SampleSize * 0.02)) { 90 } elseif ($FullAccessPermissions.Count -le $ComplianceThreshold) { 75 } else { 25 }
            Finding = if ($SampleSize -eq 0) { "No mailbox data available for assessment" } elseif ($FullAccessPermissions.Count -le $ComplianceThreshold) { "Full Access permissions appear reasonable" } else { "High number of Full Access permissions - review required" }
            Recommendation = "Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead."
            AssessmentDate = $AuditStartTime
        }

        # Perform cross-domain correlation analysis
        $CrossDomainAnalysis = $null
        if ($AdminDiscovery -and
            -not $AdminDiscovery.ContainsKey("Error") -and
            $AdminDiscovery.RoleAssignments -and
            $SampleMailboxes.Count -gt 0) {

            Write-Host "  Performing cross-domain permission correlation..." -ForegroundColor Cyan
            try {
                $CrossDomainAnalysis = Get-CrossDomainPermissionCorrelation -AdminDiscovery $AdminDiscovery -FilteredMailboxes $SampleMailboxes
                $FullAccessControl.CrossDomainAnalysis = $CrossDomainAnalysis

                # Safe property access for the success message
                $crossDomainCount = 0
                if ($CrossDomainAnalysis -and $CrossDomainAnalysis.RiskAssessment -and $CrossDomainAnalysis.RiskAssessment.CrossDomainRelationships) {
                    $crossDomainCount = $CrossDomainAnalysis.RiskAssessment.CrossDomainRelationships
                }

                Write-Host "  Cross-domain analysis completed: $crossDomainCount cross-domain relationships found" -ForegroundColor Green
            } catch {
                Write-Host "  [WARNING] Cross-domain analysis failed: $($_.Exception.Message)" -ForegroundColor Yellow
                $FullAccessControl.CrossDomainAnalysis = @{ Error = $_.Exception.Message }
            }
        } else {
            Write-Host "  [INFO] Cross-domain analysis skipped (insufficient data or administrator discovery failed)" -ForegroundColor Yellow
            $CrossDomainAnalysisResult = @{}
            $CrossDomainAnalysisResult.Status = "Skipped"
            $CrossDomainAnalysisResult.Reason = "Insufficient data or administrator discovery failed"
            $FullAccessControl.CrossDomainAnalysis = $CrossDomainAnalysisResult
        }

        $AuditResults.Add("MBX_2_1_FullAccessPermissions", $FullAccessControl)
        Write-Host "[SUCCESS] Full access permissions assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess full access permissions: $($_.Exception.Message)" -ForegroundColor Red
        $SampleMailboxes = @()  # Ensure it's available for subsequent steps
        $AuditResults.Add("MBX_2_1_FullAccessPermissions", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 3: AUDIT LOGGING CONFIGURATION
    # =============================================================================
    
    Write-Host "Step 5/7: Assessing mailbox audit logging configuration..." -ForegroundColor Yellow
    
    try {
        $AdminAuditLogConfig = Get-AdminAuditLogConfig -ErrorAction Stop
        $MailboxAuditBypassUsers = Get-MailboxAuditBypassAssociation -ErrorAction SilentlyContinue
        
        # Sample mailbox audit settings
        $SampleMailboxAuditSettings = @()
        $AuditEnabledCount = 0

        # Determine audit sample size based on parameter
        if ($PSBoundParameters.ContainsKey('AuditSampleSize') -and $AuditSampleSize -gt 0) {
            # Use specified audit sample size
            $SampleSize = if ($SampleMailboxes -and $SampleMailboxes.Count -gt 0) { [Math]::Min($AuditSampleSize, $SampleMailboxes.Count) } else { 0 }
        } else {
            # Use unlimited sampling (all available mailboxes)
            $SampleSize = if ($SampleMailboxes) { $SampleMailboxes.Count } else { 0 }
        }

        for ($i = 0; $i -lt $SampleSize; $i++) {
            try {
                $Mailbox = Get-Mailbox -Identity $SampleMailboxes[$i].Identity -ErrorAction SilentlyContinue
                if ($Mailbox.AuditEnabled) {
                    $AuditEnabledCount++
                }
                $SampleMailboxAuditSettings += @{
                    Identity = if ($Mailbox) { $Mailbox.Identity.ToString() } else { "Unknown" }
                    DisplayName = if ($Mailbox) { $Mailbox.DisplayName.ToString() } else { "Unknown" }
                    AuditEnabled = if ($Mailbox) { $Mailbox.AuditEnabled } else { $false }
                    AuditLogAgeLimit = if ($Mailbox) { $Mailbox.AuditLogAgeLimit.ToString() } else { "Unknown" }
                }
            }
            catch {
                continue
            }
        }
        
        $AuditLoggingControl = @{
            ControlID = "MBX-3.1"
            ControlName = "Mailbox Audit Logging Configuration"
            RiskLevel = "Medium"
            CVSSScore = 6.8
            AdminAuditLogEnabled = $AdminAuditLogConfig.AdminAuditLogEnabled
            MailboxAuditBypassUsers = $MailboxAuditBypassUsers | Select-Object Name, AuditBypassEnabled
            AuditBypassCount = ($MailboxAuditBypassUsers | Where-Object { $_.AuditBypassEnabled }).Count
            SampleMailboxAuditSettings = $SampleMailboxAuditSettings
            AuditEnabledMailboxes = $AuditEnabledCount
            SampleSize = $SampleSize
            CurrentValue = if ($PSBoundParameters.ContainsKey('AuditSampleSize') -and $AuditSampleSize -gt 0) {
                "Admin audit: $($AdminAuditLogConfig.AdminAuditLogEnabled), Mailbox audit enabled: $AuditEnabledCount of $SampleSize sampled"
            } else {
                "Admin audit: $($AdminAuditLogConfig.AdminAuditLogEnabled), Mailbox audit enabled: $AuditEnabledCount of $SampleSize analyzed (unlimited sampling)"
            }
            BaselineValue = "Admin and mailbox audit logging enabled organization-wide"
            ComplianceStatus = if ($AdminAuditLogConfig.AdminAuditLogEnabled -and ($AuditEnabledCount -eq $SampleSize)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($AdminAuditLogConfig.AdminAuditLogEnabled -and ($AuditEnabledCount -eq $SampleSize)) { 100 } elseif ($AdminAuditLogConfig.AdminAuditLogEnabled) { 75 } else { 25 }
            Finding = if ($AdminAuditLogConfig.AdminAuditLogEnabled) { "Admin audit logging enabled" } else { "Admin audit logging disabled - compliance risk" }
            Recommendation = if (-not $AdminAuditLogConfig.AdminAuditLogEnabled) { "Enable admin audit logging and mailbox audit for all mailboxes" } else { "Ensure mailbox audit is enabled for all mailboxes" }
            AssessmentDate = $AuditStartTime
        }
        
        $AuditResults.Add("MBX_3_1_AuditLogging", $AuditLoggingControl)
        Write-Host "[SUCCESS] Audit logging configuration assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess audit logging: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("MBX_3_1_AuditLogging", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 4: SEND-AS PERMISSIONS
    # =============================================================================

    Write-Host "Step 6/7: Assessing Send-As permissions..." -ForegroundColor Yellow

    try {
        $SendAsPermissions = @()
        $ProcessedCount = 0
        $SampleSize = if ($SampleMailboxes) { $SampleMailboxes.Count } else { 0 }

        foreach ($Mailbox in $SampleMailboxes) {
            $ProcessedCount++
            if ($ProcessedCount % $PROGRESS_REPORT_INTERVAL -eq 0) {
                Write-Host "  Processed $ProcessedCount of $SampleSize mailboxes for Send-As..." -ForegroundColor Gray
            }

            try {
                $Permissions = Get-ADPermission -Identity $Mailbox.DistinguishedName -ErrorAction SilentlyContinue |
                    Where-Object { $_.ExtendedRights -like "*Send-As*" -and $_.User -notlike "NT AUTHORITY\*" -and $_.User -notlike "S-1-*" -and $_.IsInherited -eq $false }

                foreach ($Permission in $Permissions) {
                    $SendAsPermissions += @{
                        MailboxIdentity = if ($Mailbox) { $Mailbox.Identity.ToString() } else { "Unknown" }
                        MailboxDisplayName = if ($Mailbox) { $Mailbox.DisplayName.ToString() } else { "Unknown" }
                        MailboxPrimarySmtpAddress = if ($Mailbox) { $Mailbox.PrimarySmtpAddress.ToString() } else { "Unknown" }
                        User = if ($Permission) { $Permission.User.ToString() } else { "Unknown" }
                        ExtendedRights = if ($Permission -and $Permission.ExtendedRights) { ($Permission.ExtendedRights | ForEach-Object { $_.ToString() }) -join ", " } else { "Unknown" }
                        AccessControlType = if ($Permission) { $Permission.AccessControlType.ToString() } else { "Unknown" }
                        IsInherited = if ($Permission) { $Permission.IsInherited } else { $false }
                    }
                }
            }
            catch {
                # Continue with next mailbox if permission check fails
                continue
            }
        }

        # Null-safe calculations for Send-As
        $SendAsThreshold = if ($SampleSize -gt 0) { $SampleSize * $SENDAS_THRESHOLD } else { 0 }

        $SendAsControl = @{
            ControlID = "MBX-4.1"
            ControlName = "Send-As Permissions"
            RiskLevel = "High"
            CVSSScore = 7.6
            SampleSize = $SampleSize
            TotalSendAsPermissions = $SendAsPermissions.Count
            SendAsPermissions = $SendAsPermissions | Select-Object -First 100  # Limit output size
            UniqueUsersWithSendAs = ($SendAsPermissions | Select-Object User -Unique).Count
            CurrentValue = "$($SendAsPermissions.Count) Send-As permissions found across $SampleSize mailboxes"
            BaselineValue = "Minimal Send-As permissions with business justification"
            ComplianceStatus = if ($SampleSize -eq 0) { "No Data" } elseif ($SendAsPermissions.Count -le $SendAsThreshold) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($SendAsPermissions.Count -eq 0) { 100 } elseif ($SendAsPermissions.Count -le ($SampleSize * 0.01)) { 90 } elseif ($SendAsPermissions.Count -le $SendAsThreshold) { 75 } else { 25 }
            Finding = if ($SampleSize -eq 0) { "No mailbox data available for assessment" } elseif ($SendAsPermissions.Count -le $SendAsThreshold) { "Send-As permissions appear reasonable" } else { "High number of Send-As permissions - review required" }
            Recommendation = "Regularly review Send-As permissions and ensure they are justified by business requirements"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("MBX_4_1_SendAsPermissions", $SendAsControl)
        Write-Host "[SUCCESS] Send-As permissions assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Send-As permissions: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("MBX_4_1_SendAsPermissions", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 5: SEND-ON-BEHALF PERMISSIONS
    # =============================================================================

    Write-Host "Step 7/7: Assessing Send-On-Behalf permissions..." -ForegroundColor Yellow

    try {
        $SendOnBehalfPermissions = @()
        $ProcessedCount = 0

        foreach ($Mailbox in $SampleMailboxes) {
            $ProcessedCount++
            if ($ProcessedCount % $PROGRESS_REPORT_INTERVAL -eq 0) {
                Write-Host "  Processed $ProcessedCount of $SampleSize mailboxes for Send-On-Behalf..." -ForegroundColor Gray
            }

            try {
                $MailboxDetails = Get-Mailbox -Identity $Mailbox.Identity -ErrorAction SilentlyContinue
                if ($MailboxDetails -and $MailboxDetails.GrantSendOnBehalfTo) {
                    foreach ($Delegate in $MailboxDetails.GrantSendOnBehalfTo) {
                        $SendOnBehalfPermissions += @{
                            MailboxIdentity = if ($MailboxDetails) { $MailboxDetails.Identity.ToString() } else { "Unknown" }
                            MailboxDisplayName = if ($MailboxDetails) { $MailboxDetails.DisplayName.ToString() } else { "Unknown" }
                            MailboxPrimarySmtpAddress = if ($MailboxDetails) { $MailboxDetails.PrimarySmtpAddress.ToString() } else { "Unknown" }
                            DelegateUser = if ($Delegate) { $Delegate.ToString() } else { "Unknown" }
                            PermissionType = "Send-On-Behalf"
                        }
                    }
                }
            }
            catch {
                # Continue with next mailbox if check fails
                continue
            }
        }

        # Null-safe calculations for Send-On-Behalf
        $SendOnBehalfThreshold = if ($SampleSize -gt 0) { $SampleSize * $SENDONBEHALF_THRESHOLD } else { 0 }

        $SendOnBehalfControl = @{
            ControlID = "MBX-5.1"
            ControlName = "Send-On-Behalf Permissions"
            RiskLevel = "Medium"
            CVSSScore = 6.2
            SampleSize = $SampleSize
            TotalSendOnBehalfPermissions = $SendOnBehalfPermissions.Count
            SendOnBehalfPermissions = $SendOnBehalfPermissions | Select-Object -First 100  # Limit output size
            UniqueUsersWithSendOnBehalf = ($SendOnBehalfPermissions | Select-Object DelegateUser -Unique).Count
            CurrentValue = "$($SendOnBehalfPermissions.Count) Send-On-Behalf permissions found across $SampleSize mailboxes"
            BaselineValue = "Minimal Send-On-Behalf permissions with business justification"
            ComplianceStatus = if ($SampleSize -eq 0) { "No Data" } elseif ($SendOnBehalfPermissions.Count -le $SendOnBehalfThreshold) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($SendOnBehalfPermissions.Count -eq 0) { 100 } elseif ($SendOnBehalfPermissions.Count -le ($SampleSize * 0.02)) { 90 } elseif ($SendOnBehalfPermissions.Count -le $SendOnBehalfThreshold) { 75 } else { 25 }
            Finding = if ($SampleSize -eq 0) { "No mailbox data available for assessment" } elseif ($SendOnBehalfPermissions.Count -le $SendOnBehalfThreshold) { "Send-On-Behalf permissions appear reasonable" } else { "High number of Send-On-Behalf permissions - review required" }
            Recommendation = "Regularly review Send-On-Behalf permissions and ensure they align with current business needs"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("MBX_5_1_SendOnBehalfPermissions", $SendOnBehalfControl)
        Write-Host "[SUCCESS] Send-On-Behalf permissions assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Send-On-Behalf permissions: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("MBX_5_1_SendOnBehalfPermissions", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # COMPLIANCE SUMMARY AND FINAL PROCESSING
    # =============================================================================

    Write-Host ""
    Write-Host "Generating compliance summary..." -ForegroundColor Yellow

    $AuditEndTime = Get-Date
    $AuditDuration = $AuditEndTime - $AuditStartTime

    # Calculate overall compliance metrics
    $TotalControls = 5  # Still 5 core controls (administrator discovery is supplementary data)
    $SuccessfulAssessments = ($AuditResults.Keys | Where-Object {
        $_ -ne "AuditMetadata" -and
        $_ -ne "ComplianceSummary" -and
        $_ -ne "AdministratorDiscovery" -and  # Exclude from count as it's supplementary
        (-not $AuditResults[$_].ContainsKey("Error") -or $AuditResults[$_].Error -eq $null)
    }).Count
    $FailedAssessments = $TotalControls - $SuccessfulAssessments

    $ComplianceSummary = @{
        AssessmentType = "Exchange Server Mailbox Security Controls Audit"
        TotalControlsAssessed = $TotalControls
        SuccessfulAssessments = $SuccessfulAssessments
        FailedAssessments = $FailedAssessments
        AuditStartTime = $AuditStartTime
        AuditEndTime = $AuditEndTime
        AuditDurationSeconds = [math]::Round($AuditDuration.TotalSeconds, 2)
        AuditID = $AuditID
        FrameworkVersion = "Exchange Mailbox Security Controls v1.0"
        AuditType = "Read-Only Assessment - Production Safe"
        AuditExecutedBy = $env:USERNAME
        ComputerName = $env:COMPUTERNAME
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    }

    $AuditResults.Add("ComplianceSummary", $ComplianceSummary)

    # Debug information for troubleshooting
    Write-Host ""
    Write-Host "=== AUDIT DATA COLLECTION SUMMARY ===" -ForegroundColor Yellow
    Write-Host "Total audit sections collected: $($AuditResults.Count)" -ForegroundColor White
    foreach ($key in $AuditResults.Keys) {
        $hasError = $AuditResults[$key].ContainsKey("Error")
        $status = if ($hasError) { "ERROR" } else { "SUCCESS" }
        $color = if ($hasError) { "Red" } else { "Green" }
        Write-Host "  - $key : $status" -ForegroundColor $color
    }
    Write-Host "=======================================" -ForegroundColor Yellow

    # End of main try block
} catch {
    Write-Host "[ERROR] Critical error during audit execution: $($_.Exception.Message)" -ForegroundColor Red
    $AuditResults.Add("CriticalError", @{
        Message = $_.Exception.Message
        StackTrace = $_.Exception.StackTrace
        Timestamp = Get-Date
    })
} finally {
    # =============================================================================
    # OUTPUT GENERATION AND COMPLETION
    # =============================================================================

    Write-Host ""
    Write-Host "Generating JSON output..." -ForegroundColor Yellow

    # Initialize JsonOutput variable in proper scope
    $JsonOutput = $null

    try {
        # Validate that AuditResults contains data
        if ($AuditResults.Count -eq 0) {
            Write-Host "[WARNING] No audit data collected. Creating minimal output..." -ForegroundColor Yellow
            $AuditResults.Add("EmptyResults", @{
                Message = "No audit data was collected during execution"
                Timestamp = Get-Date
                AuditID = $AuditID
            })
        }

        Write-Host "  Converting $($AuditResults.Count) audit sections to JSON..." -ForegroundColor Gray

        # Convert complex objects to serializable format first
        Write-Host "  Preparing objects for JSON serialization..." -ForegroundColor Gray
        $SerializableResults = ConvertTo-SerializableObject -InputObject $AuditResults

        # Convert results to JSON and save to file
        $JsonOutput = $SerializableResults | ConvertTo-Json -Depth 10 -ErrorAction Stop

        # Validate JSON output is not empty
        if ([string]::IsNullOrWhiteSpace($JsonOutput) -or $JsonOutput.Length -lt 10) {
            throw "JSON conversion produced empty or invalid output"
        }

        Write-Host "  JSON conversion successful. Output size: $($JsonOutput.Length) characters" -ForegroundColor Gray
        $JsonOutput | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop

        Write-Host ""
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Exchange Server Mailbox Security Audit Completed" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
        Write-Host "End Time: $((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Duration: $([math]::Round(((Get-Date) - $AuditStartTime).TotalSeconds, 2)) seconds" -ForegroundColor Cyan
        Write-Host "Controls Assessed: 5 (with cross-domain analysis)" -ForegroundColor Cyan
        if ($IsDomainFilterEnabled) {
            Write-Host "Domain Filter Applied: $($FilteredDomains -join ', ')" -ForegroundColor Cyan
        }
        Write-Host "Output File: $OutputPath" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "[SUCCESS] JSON audit results saved successfully" -ForegroundColor Green
        Write-Host "[SUCCESS] Send the file '$OutputPath' for compliance analysis" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green

        # Display summary statistics
        if ($AuditResults.ContainsKey("ComplianceSummary")) {
            $Summary = $AuditResults["ComplianceSummary"]
            Write-Host ""
            Write-Host "AUDIT SUMMARY:" -ForegroundColor Yellow
            Write-Host "- Total Controls: $($Summary.TotalControlsAssessed)" -ForegroundColor White
            Write-Host "- Successful Assessments: $($Summary.SuccessfulAssessments)" -ForegroundColor Green
            Write-Host "- Failed Assessments: $($Summary.FailedAssessments)" -ForegroundColor Red
            Write-Host "- Success Rate: $([math]::Round(($Summary.SuccessfulAssessments / $Summary.TotalControlsAssessed) * 100, 1))%" -ForegroundColor Cyan
        }

    } catch {
        Write-Host "[ERROR] Failed to save JSON output: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Error Details: $($_.Exception.GetType().Name)" -ForegroundColor Red

        # Attempt to regenerate JSON if it failed
        if ([string]::IsNullOrWhiteSpace($JsonOutput)) {
            Write-Host "Attempting to regenerate JSON output..." -ForegroundColor Yellow
            try {
                # Try with serializable objects and reduced depth
                $SerializableResults = ConvertTo-SerializableObject -InputObject $AuditResults
                $JsonOutput = $SerializableResults | ConvertTo-Json -Depth 5 -ErrorAction Stop
                Write-Host "JSON regeneration successful with reduced depth" -ForegroundColor Green
            } catch {
                Write-Host "[ERROR] JSON regeneration failed: $($_.Exception.Message)" -ForegroundColor Red
                # Create minimal JSON output
                $MinimalOutput = @{
                    AuditID = $AuditID.ToString()
                    Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
                    Error = "Failed to generate complete audit results"
                    AuditResultsKeys = ($AuditResults.Keys -join ", ")
                    AuditResultsCount = $AuditResults.Count
                }
                $JsonOutput = $MinimalOutput | ConvertTo-Json -Depth 2
            }
        }

        Write-Host "Attempting to save to alternative location..." -ForegroundColor Yellow
        try {
            $AlternativePath = ".\Exchange-Mailbox-Security-Results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
            $JsonOutput | Out-File -FilePath $AlternativePath -Encoding UTF8 -ErrorAction Stop
            Write-Host "[SUCCESS] JSON output saved to alternative location: $AlternativePath" -ForegroundColor Green
        } catch {
            Write-Host "[ERROR] Failed to save to alternative location: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Attempting to save to current directory..." -ForegroundColor Yellow
            try {
                $EmergencyPath = ".\Emergency-Audit-Results.json"
                $JsonOutput | Out-File -FilePath $EmergencyPath -Encoding UTF8 -ErrorAction Stop
                Write-Host "[SUCCESS] Emergency save successful: $EmergencyPath" -ForegroundColor Green
            } catch {
                Write-Host "[ERROR] All save attempts failed. Displaying results to console:" -ForegroundColor Red
                Write-Host "=== AUDIT RESULTS ===" -ForegroundColor Yellow
                Write-Host $JsonOutput
                Write-Host "=== END AUDIT RESULTS ===" -ForegroundColor Yellow
            }
        }
    }
}

<#
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN (Exchange Server):
================================================================================

0. DISPLAY HELP INFORMATION:
   .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -Help
   .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1 -h

1. EXCHANGE SERVER LOCAL EXECUTION (Recommended):
   .\Exchange-Mailbox-Security-Audit-v1.7.0.ps1

2. WITH DOMAIN FILTERING (Single Domain):
   .\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "domain.com"

3. WITH DOMAIN FILTERING (Multiple Domains):
   .\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter @("Xyyyya.xx", "home.hm", "domain.com")

4. WITH CUSTOM OUTPUT PATH:
   .\Exchange-Mailbox-Security-Audit.ps1 -OutputPath "C:\Temp\Mailbox-Security-Results.json"

5. WITH LARGER MAILBOX SAMPLE:
   .\Exchange-Mailbox-Security-Audit.ps1 -MaxMailboxSample 1000

6. WITH LIMITED AUDIT SAMPLING (for performance in large environments):
   .\Exchange-Mailbox-Security-Audit.ps1 -AuditSampleSize 100

7. WITH UNLIMITED AUDIT SAMPLING (default - analyzes all available records):
   .\Exchange-Mailbox-Security-Audit.ps1
   # Note: Omitting -AuditSampleSize enables comprehensive auditing

8. COMBINED PARAMETERS:
   .\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "domain.com" -MaxMailboxSample 1000 -AuditSampleSize 200 -OutputPath "C:\Temp\Results.json"

9. REMOTE EXECUTION (Exchange Management Shell):
   # Connect to Exchange first, then run the script
   $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://ExchangeServer/PowerShell/
   Import-PSSession $Session
   .\Exchange-Mailbox-Security-Audit.ps1

PREREQUISITES:
- Exchange Server 2016/2019 or Exchange Online
- Exchange Management Shell or PowerShell with Exchange module
- User account with appropriate Exchange permissions:
  * View-Only Organization Management (minimum)
  * Organization Management (for comprehensive assessment)
- PowerShell 5.1 or later

PARAMETERS:
-OutputPath: Specifies the output JSON file path (default: .\Exchange-Mailbox-Security-Results.json)
-MaxMailboxSample: Maximum number of mailboxes to retrieve for permission analysis (default: 500)
-AuditSampleSize: Optional parameter to limit audit configuration analysis
  * When omitted: Analyzes ALL available mailboxes (unlimited sampling - recommended for comprehensive audits)
  * When specified: Limits audit analysis to the specified number of mailboxes (useful for performance in large environments)
  * Performance Note: Values over 1000 may cause performance issues in very large environments
-DomainFilter: Optional array of domain names to filter mailboxes (e.g., @("domain1.com", "domain2.com"))

IMPORTANT NOTES:
- This script is 100% READ-ONLY and safe for production
- No Exchange configuration modifications are performed
- Output is in JSON format for easy processing
- Script assesses 5 critical mailbox security controls:
  1. Mailbox Impersonation Rights (ApplicationImpersonation role)
  2. Full Access Permissions (mailbox delegations)
  3. Audit Logging Configuration (admin and mailbox audit)
  4. Send-As Permissions (Send-As rights)
  5. Send-On-Behalf Permissions (Send-On-Behalf delegations)
- Compatible with Exchange 2016, 2019, and Exchange Online
- All operations use only Get-* cmdlets and read-only functions

SEND BACK: The generated Exchange-Mailbox-Security-Results.json file

SECURITY VERIFICATION:
- Only Get-* cmdlets used (Get-ManagementRoleAssignment, Get-Mailbox, Get-MailboxPermission, etc.)
- No Set-*, New-*, Remove-*, or Enable-* cmdlets
- No configuration changes or data modifications
- Read-only access to Exchange mailbox configuration and permissions
- Safe for production environments

ASSESSMENT FOCUS:
This script specifically focuses on mailbox access security rather than broader
Exchange configuration. It identifies potential security risks related to:
- Excessive mailbox access permissions
- Impersonation rights that could be misused
- Inadequate audit logging for compliance
- Delegation permissions that may violate least privilege principles

ENHANCED CROSS-DOMAIN ANALYSIS (Version 1.6):
- Administrator Discovery: Maps ALL Exchange administrators regardless of domain affiliation
- Cross-Domain Permission Correlation: Identifies administrators from any domain with rights to filtered mailboxes
- Administrative vs. Business User Classification: Distinguishes between admin and delegated access
- Risk Scoring: Evaluates cross-domain relationships for security implications
- Compliance Assessment: Validates audit logging across domain boundaries

DOMAIN FILTERING FEATURE:
The -DomainFilter parameter allows targeted auditing of specific email domains:
- Supports single domain: -DomainFilter "domain.com"
- Supports multiple domains: -DomainFilter @("domain1.com", "domain2.com")
- When enabled, only mailboxes from specified domains are audited
- Improves performance in large multi-domain environments
- Maintains backward compatibility when parameter is not used

================================================================================
#>
# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.6.6
# **Exchange Version:** Exchange Server 2016
# 🏛️ **Authority:** Internal Audit
# ================================================================================
