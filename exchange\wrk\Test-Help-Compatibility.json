﻿{
    "AdministratorDiscovery":  {
                                   "CrossDomainRelationships":  {

                                                                },
                                   "AdminDomainMap":  {

                                                      },
                                   "RoleAssignments":  {

                                                       },
                                   "AdminClassification":  {

                                                           }
                               },
    "CriticalError":  {
                          "Message":  "Exchange PowerShell module required",
                          "Timestamp":  "2025-09-16 00:57:08",
                          "StackTrace":  null
                      },
    "AuditMetadata":  {
                          "Error":  "The term \u0027Get-OrganizationConfig\u0027 is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again."
                      }
}
