﻿================================================================================
EXCHANGE MAILBOX SECURITY AUDIT REPORT
================================================================================

Report Generated: 2025-09-15 23:42:43
Source JSON File: Sample-Exchange-Mailbox-Security-Results-50-Realistic.json
Conversion Script: Exchange JSON to TXT Converter v2.0.0
Created By: E.Z. Consultancy

================================================================================
AUDIT METADATA
================================================================================

Audit ID: b8f3e2d1-4c5a-6789-bcde-f012345678ab
Organization: TechCorp International
Exchange Version: 
Audit Start Time: 2025-09-14 10:15:30
Audit End Time: 
Audit Duration: 
Script Version: 1.6.7
Script Release Date: September 11, 2025
Script Author: E.Z. Consultancy
PowerShell Version: 5.1.19041.4648
PowerShell Edition: Desktop
Executed By: 
Execution Host: EXCH-MGMT-02
Total Mailboxes: 847
Max Mailbox Records: 500
Audit Scope: 5 Critical Mailbox Security Controls
Audit User: svc-audit
Domain Filter Enabled: True
Filtered Domains: techcorp.com, subsidiary.techcorp.com
Domain Filter Scope: Domain-specific audit for: techcorp.com, subsidiary.techcorp.com
Exchange Servers: 4

================================================================================
EXECUTIVE SUMMARY
================================================================================

Overall Compliance Score: 83.2%
Assessment Success Rate: 100%

CONTROL ASSESSMENT SUMMARY:
  Total Controls Assessed: 5
  Controls Compliant: 2
  Controls Requiring Review: 3
  Controls Non-Compliant: 0
  Controls with No Data: 0

RISK ASSESSMENT SUMMARY:
  Critical Findings: 1
  High Risk Findings: 2
  Medium Risk Findings: 2
  Low Risk Findings: 0

AUDIT EXECUTION SUMMARY:
  Successful Assessments: 5
  Failed Assessments: 0
  Audit Duration: 1035.67 seconds
  Assessment Type: Exchange Server Mailbox Security Controls Audit

================================================================================
MAILBOX IMPERSONATION RIGHTS (MBX-1.1)
================================================================================

Control ID: MBX-1.1
Risk Level: Critical
CVSS Score: 9.3
Compliance Status: Review Required
Compliance Score: 75%
Sample Size: 

FINDING:
  Limited impersonation rights - review assignments

RECOMMENDATION:
  Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions

IMPERSONATION RIGHTS COMPREHENSIVE DETAILS:
  Total Impersonation Assignments: 4

  COMPLETE IMPERSONATION RIGHTS LISTING:
    [1] Impersonation Assignment:
        Role Assignee: Exchange Backup Service
        Full DN: TECHCORP\svc-exchange-backup
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-01-15 09:30:00
        Is Valid: True
        Description: Exchange Backup Service has ApplicationImpersonation rights across All Mailboxes (Organization-Wide)
        Summary: Account Type: User | Assignment Method: Direct | Created: 2025-01-15 09:30:00 | Config: Valid

        Action Plan: 


    [2] Impersonation Assignment:
        Role Assignee: Compliance Service Account
        Full DN: TECHCORP\svc-compliance
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-02-20 14:15:00
        Is Valid: True
        Description: Compliance Service Account has ApplicationImpersonation rights across All Mailboxes (Organization-Wide)
        Summary: Account Type: User | Assignment Method: Direct | Created: 2025-02-20 14:15:00 | Config: Valid

        Action Plan: 


    [3] Impersonation Assignment:
        Role Assignee: Exchange Administrator
        Full DN: TECHCORP\admin-exchange
        Assignee Type: User
        Assignment Method: RoleGroup
        When Created: 2025-01-10 11:45:00
        Is Valid: True
        Description: Exchange Administrator has ApplicationImpersonation rights across All Mailboxes (Organization-Wide)
        Summary: Account Type: User | Assignment Method: RoleGroup | Created: 2025-01-10 11:45:00 | Config: Valid

        Action Plan: 


    [4] Impersonation Assignment:
        Role Assignee: Exchange Monitoring Service
        Full DN: TECHCORP\svc-monitoring
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-03-05 16:20:00
        Is Valid: True
        Description: Exchange Monitoring Service has ApplicationImpersonation rights across All Mailboxes (Organization-Wide)
        Summary: Account Type: User | Assignment Method: Direct | Created: 2025-03-05 16:20:00 | Config: Valid

        Action Plan: 



================================================================================
MAILBOX FULL ACCESS PERMISSIONS (MBX-2.1)
================================================================================

Control ID: MBX-2.1
Risk Level: High
CVSS Score: 8.1
Compliance Status: Review Required
Compliance Score: 72%
Sample Size: 50

FINDING:
  High number of Full Access permissions - review required

RECOMMENDATION:
  Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.

FULL ACCESS PERMISSIONS COMPREHENSIVE DETAILS:
  Total Full Access Permissions: 89
  Unique Users with Full Access: 67

  COMPLETE FULL ACCESS PERMISSIONS LISTING:
    [1] Full Access Entry:
        Mailbox: John Doe
        Email: <EMAIL>
        User Account: TECHCORP\jane.smith
        Access Rights: FullAccess
        Inherited: False
        Description: jane.smith has full access to John Doe (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [2] Full Access Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Access Rights: FullAccess
        Inherited: False
        Description: executive.assistant has full access to Chief Executive Officer (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [3] Full Access Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        User Account: TECHCORP\robert.wilson
        Access Rights: FullAccess
        Inherited: False
        Description: robert.wilson has full access to Finance Team (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [4] Full Access Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        User Account: TECHCORP\sarah.johnson
        Access Rights: FullAccess
        Inherited: False
        Description: sarah.johnson has full access to HR Shared Mailbox (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [5] Full Access Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        User Account: TECHCORP\mike.anderson
        Access Rights: FullAccess
        Inherited: False
        Description: mike.anderson has full access to IT Support (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [6] Full Access Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        User Account: TECHCORP\lisa.brown
        Access Rights: FullAccess
        Inherited: False
        Description: lisa.brown has full access to Marketing Department (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [7] Full Access Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        User Account: TECHCORP\david.martinez
        Access Rights: FullAccess
        Inherited: False
        Description: david.martinez has full access to Sales Team (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [8] Full Access Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        User Account: TECHCORP\amanda.taylor
        Access Rights: FullAccess
        Inherited: False
        Description: amanda.taylor has full access to Legal Department (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [9] Full Access Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        User Account: TECHCORP\thomas.white
        Access Rights: FullAccess
        Inherited: False
        Description: thomas.white has full access to Procurement Team (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [10] Full Access Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        User Account: TECHCORP\patricia.clark
        Access Rights: FullAccess
        Inherited: False
        Description: patricia.clark has full access to Quality Assurance (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [11] Full Access Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        User Account: TECHCORP\nancy.rodriguez
        Access Rights: FullAccess
        Inherited: False
        Description: nancy.rodriguez has full access to Customer Service (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [12] Full Access Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        User Account: TECHCORP\kevin.lewis
        Access Rights: FullAccess
        Inherited: False
        Description: kevin.lewis has full access to Research & Development (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [13] Full Access Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        User Account: TECHCORP\stephanie.walker
        Access Rights: FullAccess
        Inherited: False
        Description: stephanie.walker has full access to Training Department (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [14] Full Access Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        User Account: TECHCORP\brian.hall
        Access Rights: FullAccess
        Inherited: False
        Description: brian.hall has full access to Security Team (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [15] Full Access Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        User Account: TECHCORP\michelle.young
        Access Rights: FullAccess
        Inherited: False
        Description: michelle.young has full access to Operations Manager (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [16] Full Access Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        User Account: TECHCORP\daniel.king
        Access Rights: FullAccess
        Inherited: False
        Description: daniel.king has full access to Accounting Department (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [17] Full Access Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        User Account: TECHCORP\carol.wright
        Access Rights: FullAccess
        Inherited: False
        Description: carol.wright has full access to Project Management Office (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [18] Full Access Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False
        Description: facilities.admin has full access to Facilities Management (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [19] Full Access Entry:
        Mailbox: Conference Room A
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False
        Description: facilities.admin has full access to Conference Room A (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [20] Full Access Entry:
        Mailbox: Conference Room B
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False
        Description: facilities.admin has full access to Conference Room B (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [21] Full Access Entry:
        Mailbox: Executive Board Room
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Access Rights: FullAccess
        Inherited: False
        Description: executive.assistant has full access to Executive Board Room (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [22] Full Access Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        User Account: SUBSIDIARY\external.user
        Access Rights: FullAccess
        Inherited: False
        Description: external.user has full access to Shared Project Alpha (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [23] Full Access Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        User Account: TECHCORP\procurement.manager
        Access Rights: FullAccess
        Inherited: False
        Description: procurement.manager has full access to Vendor Communications (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [24] Full Access Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        User Account: TECHCORP\svc-compliance
        Access Rights: FullAccess
        Inherited: False
        Description: svc-compliance has full access to Compliance Officer (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 


    [25] Full Access Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        User Account: TECHCORP\audit.manager
        Access Rights: FullAccess
        Inherited: False
        Description: audit.manager has full access to Internal Audit (<EMAIL>)
        Summary: Assignment: Direct | Rights: FullAccess | Justified: No

        Action Plan: 



CROSS-DOMAIN ANALYSIS:

  CROSS-DOMAIN RELATIONSHIPS:
    [1] Cross-Domain Permission:
        Mailbox: Shared Project Alpha
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\external.user
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 5
        Risk Level: High
        Permission Type: Direct
        Inherited: False
        Description: User 'external.user' from domain 'subsidiary.techcorp.com' has FullAccess access to mailbox 'Shared Project Alpha' in domain 'techcorp.com'. This represents a HIGH SECURITY RISK as it allows cross-domain administrative access.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: FullAccess | Risk: High | Classification: Business-User-Delegate

        Action Plan: 


    [2] Cross-Domain Permission:
        Mailbox: Vendor Communications
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\vendor.admin
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False
        Description: User 'vendor.admin' from domain 'subsidiary.techcorp.com' has FullAccess access to mailbox 'Vendor Communications' in domain 'techcorp.com'. This represents a MODERATE SECURITY RISK requiring review and justification.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: FullAccess | Risk: Medium | Classification: Business-User-Delegate

        Action Plan: 


    [3] Cross-Domain Permission:
        Mailbox: Finance Team
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\finance.auditor
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Exchange-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 6
        Risk Level: High
        Permission Type: Direct
        Inherited: False
        Description: User 'finance.auditor' from domain 'subsidiary.techcorp.com' has FullAccess access to mailbox 'Finance Team' in domain 'techcorp.com'. This represents a HIGH SECURITY RISK as it allows cross-domain administrative access.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: FullAccess | Risk: High | Classification: Exchange-Administrator

        Action Plan: 


    [4] Cross-Domain Permission:
        Mailbox: Compliance Officer
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\compliance.manager
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Read-Only-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False
        Description: User 'compliance.manager' from domain 'subsidiary.techcorp.com' has FullAccess access to mailbox 'Compliance Officer' in domain 'techcorp.com'. This represents a MODERATE SECURITY RISK requiring review and justification.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: FullAccess | Risk: Medium | Classification: Read-Only-Administrator

        Action Plan: 


    [5] Cross-Domain Permission:
        Mailbox: Chief Executive Officer
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\board.secretary
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-As
        Is Cross-Domain: True
        Risk Score: 7
        Risk Level: High
        Permission Type: Direct
        Inherited: False
        Description: User 'board.secretary' from domain 'subsidiary.techcorp.com' has Send-As access to mailbox 'Chief Executive Officer' in domain 'techcorp.com'. This represents a HIGH SECURITY RISK as it allows cross-domain administrative access.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: Send-As | Risk: High | Classification: Business-User-Delegate

        Action Plan: 


    [6] Cross-Domain Permission:
        Mailbox: Legal Department
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\legal.counsel
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-On-Behalf
        Is Cross-Domain: True
        Risk Score: 3
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False
        Description: User 'legal.counsel' from domain 'subsidiary.techcorp.com' has Send-On-Behalf access to mailbox 'Legal Department' in domain 'techcorp.com'. This represents a MODERATE SECURITY RISK requiring review and justification.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: Send-On-Behalf | Risk: Medium | Classification: Business-User-Delegate

        Action Plan: 


    [7] Cross-Domain Permission:
        Mailbox: Internal Audit
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\external.auditor
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Exchange-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 6
        Risk Level: High
        Permission Type: Direct
        Inherited: False
        Description: User 'external.auditor' from domain 'subsidiary.techcorp.com' has FullAccess access to mailbox 'Internal Audit' in domain 'techcorp.com'. This represents a HIGH SECURITY RISK as it allows cross-domain administrative access.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: FullAccess | Risk: High | Classification: Exchange-Administrator

        Action Plan: 


    [8] Cross-Domain Permission:
        Mailbox: HR Shared Mailbox
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\hr.consultant
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-As
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False
        Description: User 'hr.consultant' from domain 'subsidiary.techcorp.com' has Send-As access to mailbox 'HR Shared Mailbox' in domain 'techcorp.com'. This represents a MODERATE SECURITY RISK requiring review and justification.
        Summary: Cross-Domain: subsidiary.techcorp.com â†’ techcorp.com | Access: Send-As | Risk: Medium | Classification: Business-User-Delegate

        Action Plan: 


  PERMISSION SUMMARY:
    Domain Relationship: subsidiary.techcorp.com -> techcorp.com
      Total Count: 8
      Admin Domain: subsidiary.techcorp.com
      Mailbox Domain: techcorp.com
      High Risk Count: 4
      Admin Types:
        Business-User-Delegate: 5
        Exchange-Administrator: 2
        Read-Only-Administrator: 1

  RISK ASSESSMENT:
    Total Permission Relationships: 89
    Cross-Domain Relationships: 8
    High Risk Relationships: 4
    Cross-Domain Percentage: 8.99%
    Risk Distribution:
      High: 4
      Medium: 4
      Low: 81


================================================================================
MAILBOX AUDIT LOGGING CONFIGURATION (MBX-3.1)
================================================================================

Control ID: MBX-3.1
Risk Level: Medium
CVSS Score: 6.8
Compliance Status: Review Required
Compliance Score: 96%
Sample Size: 50

FINDING:
  Admin audit logging enabled

RECOMMENDATION:
  Ensure mailbox audit is enabled for all mailboxes

AUDIT LOGGING COMPREHENSIVE DETAILS:
  Admin Audit Logging Enabled: True
  Mailboxes with Audit Enabled: 48
  Audit Bypass Users Count: 2
  Sample Size: 50

  MAILBOX AUDIT BYPASS USERS:
    [1] User: svc-exchange-backup (Bypass: Enabled)
    [2] User: svc-monitoring (Bypass: Enabled)

  MAILBOX AUDIT SETTINGS:
    Total Audited Mailboxes: 49

    [1] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: John Doe
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [2] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Executive Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [3] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Finance Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [4] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Financial Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [5] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Technology Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [6] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: HR Shared Mailbox
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [7] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: IT Support
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [8] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Marketing Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [9] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Sales Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [10] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Legal Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [11] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Procurement Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [12] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Quality Assurance
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [13] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Customer Service
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [14] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Research & Development
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [15] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Training Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [16] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Security Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [17] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Operations Manager
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [18] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Accounting Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [19] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Project Management Office
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [20] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Facilities Management
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [21] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Conference Room A
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [22] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Conference Room B
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [23] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Executive Board Room
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [24] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Shared Project Alpha
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [25] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Vendor Communications
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [26] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Compliance Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [27] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Internal Audit
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [28] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Jane Smith
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [29] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Robert Wilson
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [30] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Sarah Johnson
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [31] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Mike Anderson
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [32] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Lisa Brown
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [33] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: David Martinez
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [34] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Amanda Taylor
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [35] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Thomas White
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [36] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Patricia Clark
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [37] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Nancy Rodriguez
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [38] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Kevin Lewis
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [39] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Stephanie Walker
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [40] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Brian Hall
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [41] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Michelle Young
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [42] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Daniel King
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [43] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Carol Wright
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [44] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Jennifer Garcia
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [45] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Executive Assistant
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [46] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Finance Manager
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [47] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: IT Director
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [48] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Test Mailbox
        Audit Enabled: False
        Audit Log Age Limit: 90.00:00:00

    [49] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Legacy System Account
        Audit Enabled: False
        Audit Log Age Limit: 90.00:00:00

  CURRENT ASSESSMENT:
    Admin audit: True, Mailbox audit enabled: 48 of 50 sampled


================================================================================
SEND-AS PERMISSIONS (MBX-4.1)
================================================================================

Control ID: MBX-4.1
Risk Level: High
CVSS Score: 7.6
Compliance Status: Compliant
Compliance Score: 85%
Sample Size: 50

FINDING:
  Send-As permissions appear reasonable

RECOMMENDATION:
  Regularly review Send-As permissions and ensure they are justified by business requirements

SEND-AS PERMISSIONS COMPREHENSIVE DETAILS:
  Total Send-As Permissions: 23
  Unique Users with Send-As: 18

  COMPLETE SEND-AS PERMISSIONS LISTING:
    [1] Send-As Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Inherited: False
        Description: executive.assistant can send as Chief Executive Officer (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [2] Send-As Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        User Account: TECHCORP\robert.wilson
        Inherited: False
        Description: robert.wilson can send as Finance Team (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [3] Send-As Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        User Account: TECHCORP\lisa.brown
        Inherited: False
        Description: lisa.brown can send as Marketing Department (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [4] Send-As Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        User Account: TECHCORP\david.martinez
        Inherited: False
        Description: david.martinez can send as Sales Team (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [5] Send-As Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        User Account: TECHCORP\sarah.johnson
        Inherited: False
        Description: sarah.johnson can send as HR Shared Mailbox (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [6] Send-As Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        User Account: TECHCORP\amanda.taylor
        Inherited: False
        Description: amanda.taylor can send as Legal Department (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [7] Send-As Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        User Account: TECHCORP\mike.anderson
        Inherited: False
        Description: mike.anderson can send as IT Support (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [8] Send-As Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        User Account: TECHCORP\nancy.rodriguez
        Inherited: False
        Description: nancy.rodriguez can send as Customer Service (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [9] Send-As Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        User Account: TECHCORP\thomas.white
        Inherited: False
        Description: thomas.white can send as Procurement Team (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [10] Send-As Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        User Account: TECHCORP\stephanie.walker
        Inherited: False
        Description: stephanie.walker can send as Training Department (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [11] Send-As Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        User Account: TECHCORP\patricia.clark
        Inherited: False
        Description: patricia.clark can send as Quality Assurance (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [12] Send-As Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        User Account: TECHCORP\kevin.lewis
        Inherited: False
        Description: kevin.lewis can send as Research & Development (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [13] Send-As Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        User Account: TECHCORP\brian.hall
        Inherited: False
        Description: brian.hall can send as Security Team (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [14] Send-As Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        User Account: TECHCORP\michelle.young
        Inherited: False
        Description: michelle.young can send as Operations Manager (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [15] Send-As Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        User Account: TECHCORP\daniel.king
        Inherited: False
        Description: daniel.king can send as Accounting Department (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [16] Send-As Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        User Account: TECHCORP\carol.wright
        Inherited: False
        Description: carol.wright can send as Project Management Office (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [17] Send-As Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        User Account: TECHCORP\svc-compliance
        Inherited: False
        Description: svc-compliance can send as Compliance Officer (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [18] Send-As Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        User Account: TECHCORP\audit.manager
        Inherited: False
        Description: audit.manager can send as Internal Audit (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [19] Send-As Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        User Account: TECHCORP\procurement.manager
        Inherited: False
        Description: procurement.manager can send as Vendor Communications (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [20] Send-As Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Inherited: False
        Description: facilities.admin can send as Facilities Management (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [21] Send-As Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        User Account: SUBSIDIARY\external.user
        Inherited: False
        Description: external.user can send as Shared Project Alpha (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [22] Send-As Entry:
        Mailbox: Chief Financial Officer
        Email: <EMAIL>
        User Account: TECHCORP\finance.manager
        Inherited: False
        Description: finance.manager can send as Chief Financial Officer (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 


    [23] Send-As Entry:
        Mailbox: Chief Technology Officer
        Email: <EMAIL>
        User Account: TECHCORP\it.director
        Inherited: False
        Description: it.director can send as Chief Technology Officer (<EMAIL>)
        Summary: Assignment: Direct | Justified: No

        Action Plan: 



================================================================================
SEND-ON-BEHALF PERMISSIONS (MBX-5.1)
================================================================================

Control ID: MBX-5.1
Risk Level: Medium
CVSS Score: 6.2
Compliance Status: Compliant
Compliance Score: 88%
Sample Size: 50

FINDING:
  Send-On-Behalf permissions appear reasonable

RECOMMENDATION:
  Regularly review Send-On-Behalf permissions and ensure they align with current business needs

SEND-ON-BEHALF PERMISSIONS COMPREHENSIVE DETAILS:
  Total Send-On-Behalf Permissions: 34
  Unique Users with Send-On-Behalf: 28

  COMPLETE SEND-ON-BEHALF PERMISSIONS LISTING:
    [1] Send-On-Behalf Entry:
        Mailbox: Chief Financial Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\finance.manager
        Description: finance.manager can send on behalf of Chief Financial Officer (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [2] Send-On-Behalf Entry:
        Mailbox: HR Director
        Email: <EMAIL>
        Delegate User: TECHCORP\sarah.johnson
        Description: sarah.johnson can send on behalf of HR Director (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [3] Send-On-Behalf Entry:
        Mailbox: Sales Manager
        Email: <EMAIL>
        Delegate User: TECHCORP\jennifer.garcia
        Description: jennifer.garcia can send on behalf of Sales Manager (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [4] Send-On-Behalf Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\executive.assistant
        Description: executive.assistant can send on behalf of Chief Executive Officer (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [5] Send-On-Behalf Entry:
        Mailbox: Chief Technology Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\it.director
        Description: it.director can send on behalf of Chief Technology Officer (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [6] Send-On-Behalf Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        Delegate User: TECHCORP\lisa.brown
        Description: lisa.brown can send on behalf of Marketing Department (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [7] Send-On-Behalf Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        Delegate User: TECHCORP\michelle.young
        Description: michelle.young can send on behalf of Operations Manager (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [8] Send-On-Behalf Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        Delegate User: TECHCORP\amanda.taylor
        Description: amanda.taylor can send on behalf of Legal Department (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [9] Send-On-Behalf Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        Delegate User: TECHCORP\thomas.white
        Description: thomas.white can send on behalf of Procurement Team (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [10] Send-On-Behalf Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        Delegate User: TECHCORP\patricia.clark
        Description: patricia.clark can send on behalf of Quality Assurance (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [11] Send-On-Behalf Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        Delegate User: TECHCORP\kevin.lewis
        Description: kevin.lewis can send on behalf of Research & Development (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [12] Send-On-Behalf Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        Delegate User: TECHCORP\stephanie.walker
        Description: stephanie.walker can send on behalf of Training Department (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [13] Send-On-Behalf Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        Delegate User: TECHCORP\brian.hall
        Description: brian.hall can send on behalf of Security Team (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [14] Send-On-Behalf Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        Delegate User: TECHCORP\daniel.king
        Description: daniel.king can send on behalf of Accounting Department (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [15] Send-On-Behalf Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        Delegate User: TECHCORP\carol.wright
        Description: carol.wright can send on behalf of Project Management Office (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [16] Send-On-Behalf Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin
        Description: facilities.admin can send on behalf of Facilities Management (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [17] Send-On-Behalf Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        Delegate User: TECHCORP\nancy.rodriguez
        Description: nancy.rodriguez can send on behalf of Customer Service (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [18] Send-On-Behalf Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        Delegate User: TECHCORP\mike.anderson
        Description: mike.anderson can send on behalf of IT Support (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [19] Send-On-Behalf Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\svc-compliance
        Description: svc-compliance can send on behalf of Compliance Officer (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [20] Send-On-Behalf Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        Delegate User: TECHCORP\audit.manager
        Description: audit.manager can send on behalf of Internal Audit (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [21] Send-On-Behalf Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        Delegate User: TECHCORP\procurement.manager
        Description: procurement.manager can send on behalf of Vendor Communications (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [22] Send-On-Behalf Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        Delegate User: SUBSIDIARY\external.user
        Description: external.user can send on behalf of Shared Project Alpha (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [23] Send-On-Behalf Entry:
        Mailbox: John Doe
        Email: <EMAIL>
        Delegate User: TECHCORP\jane.smith
        Description: jane.smith can send on behalf of John Doe (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [24] Send-On-Behalf Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        Delegate User: TECHCORP\robert.wilson
        Description: robert.wilson can send on behalf of Finance Team (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [25] Send-On-Behalf Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        Delegate User: TECHCORP\david.martinez
        Description: david.martinez can send on behalf of Sales Team (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [26] Send-On-Behalf Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        Delegate User: TECHCORP\sarah.johnson
        Description: sarah.johnson can send on behalf of HR Shared Mailbox (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [27] Send-On-Behalf Entry:
        Mailbox: Executive Board Room
        Email: <EMAIL>
        Delegate User: TECHCORP\executive.assistant
        Description: executive.assistant can send on behalf of Executive Board Room (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [28] Send-On-Behalf Entry:
        Mailbox: Conference Room A
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin
        Description: facilities.admin can send on behalf of Conference Room A (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [29] Send-On-Behalf Entry:
        Mailbox: Conference Room B
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin
        Description: facilities.admin can send on behalf of Conference Room B (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [30] Send-On-Behalf Entry:
        Mailbox: General Information
        Email: <EMAIL>
        Delegate User: TECHCORP\customer.service.lead
        Description: customer.service.lead can send on behalf of General Information (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [31] Send-On-Behalf Entry:
        Mailbox: Technical Support
        Email: <EMAIL>
        Delegate User: TECHCORP\support.manager
        Description: support.manager can send on behalf of Technical Support (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [32] Send-On-Behalf Entry:
        Mailbox: No Reply System
        Email: <EMAIL>
        Delegate User: TECHCORP\system.admin
        Description: system.admin can send on behalf of No Reply System (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [33] Send-On-Behalf Entry:
        Mailbox: System Alerts
        Email: <EMAIL>
        Delegate User: TECHCORP\monitoring.service
        Description: monitoring.service can send on behalf of System Alerts (<EMAIL>)
        Summary: Justified: No

        Action Plan: 


    [34] Send-On-Behalf Entry:
        Mailbox: Backup Reports
        Email: <EMAIL>
        Delegate User: TECHCORP\svc-backup
        Description: svc-backup can send on behalf of Backup Reports (<EMAIL>)
        Summary: Justified: No

        Action Plan: 



================================================================================
ADMINISTRATOR DISCOVERY
================================================================================

EXCHANGE ROLE ASSIGNMENTS:
  Total Role Assignments: 6

  [1] Role Assignment:
      Role Assignee: TECHCORP\admin-exchange
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Organization Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: Direct

  [2] Role Assignment:
      Role Assignee: TECHCORP\svc-compliance
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Discovery Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Read-Only-Administrator
      Assignment Method: Direct

  [3] Role Assignment:
      Role Assignee: TECHCORP\admin-server
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Server Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: RoleGroup

  [4] Role Assignment:
      Role Assignee: TECHCORP\admin-recipient
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Recipient Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Mailbox-Administrator
      Assignment Method: RoleGroup

  [5] Role Assignment:
      Role Assignee: SUBSIDIARY\external.auditor
      Assignee Domain: subsidiary.techcorp.com
      Assignee Type: User
      Role: View-Only Organization Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Read-Only-Administrator
      Assignment Method: Direct

  [6] Role Assignment:
      Role Assignee: SUBSIDIARY\finance.auditor
      Assignee Domain: subsidiary.techcorp.com
      Assignee Type: User
      Role: Discovery Management
      Role Type: Management
      Scope Type: Custom-Scope
      Affected Domains: techcorp.com
      Organization-Wide: False
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: Direct

ADMINISTRATOR DOMAIN MAPPING:
  Domain: techcorp.com
    Administrator Count: 4
      - TECHCORP\admin-exchange (Exchange-Administrator)
      - TECHCORP\svc-compliance (Read-Only-Administrator)
      - TECHCORP\admin-server (Exchange-Administrator)
      - TECHCORP\admin-recipient (Mailbox-Administrator)

  Domain: subsidiary.techcorp.com
    Administrator Count: 2
      - SUBSIDIARY\external.auditor (Read-Only-Administrator)
      - SUBSIDIARY\finance.auditor (Exchange-Administrator)

CROSS-DOMAIN ADMINISTRATIVE RELATIONSHIPS:
  [1] Cross-Domain Admin Relationship:
      Admin Domain: subsidiary.techcorp.com
      Target Domain: techcorp.com
      Relationship Type: Cross-Domain-Administration
      Risk Level: High
      Admin Count: 2

ADMINISTRATOR CLASSIFICATION SUMMARY:
  TECHCORP\admin-exchange: Exchange-Administrator
  TECHCORP\svc-compliance: Read-Only-Administrator
  TECHCORP\svc-backup: Business-User
  TECHCORP\svc-monitoring: Business-User
  TECHCORP\admin-server: Exchange-Administrator
  TECHCORP\admin-recipient: Mailbox-Administrator
  SUBSIDIARY\external.auditor: Read-Only-Administrator
  SUBSIDIARY\finance.auditor: Exchange-Administrator

CLASSIFICATION SUMMARY:
  Business-User: 2 administrators
  Mailbox-Administrator: 1 administrators
  Exchange-Administrator: 3 administrators
  Read-Only-Administrator: 2 administrators

================================================================================
END OF REPORT
================================================================================

Report Generation Completed: 2025-09-15 23:42:43
Total Processing Time: 00:00:00.2357221

This comprehensive report contains all detailed audit information including:
  - Complete permission listings for all security controls
  - Full user and mailbox metadata for each permission assignment
  - Detailed audit logging configuration and compliance status
  - Business justifications and assignment dates where available
  - Department and organizational context for all findings

Generated by: Exchange Audit Tools - E.Z. Consultancy
Script Version: 2.0.0
================================================================================
