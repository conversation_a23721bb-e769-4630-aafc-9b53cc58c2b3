#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 2.0.0
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit - JSON to Text Report Converter
# ================================================================================

<#
================================================================================
Exchange Mailbox Security Audit JSON to TXT Converter
================================================================================
Description: Converts Exchange mailbox security audit JSON output files into
comprehensive human-readable text reports for security analysis and compliance.

This script processes the JSON output from Exchange-Mailbox-Security-Audit.ps1
version 1.6.7 and creates a single, detailed text report containing:
- Executive summary with compliance scores and risk assessments
- Complete audit metadata and environment information
- Comprehensive details for all security controls (MBX-1.1 through MBX-5.1)
- Full permission listings with all available metadata
- Professional formatting suitable for security analysts and auditors

INSTRUCTIONS FOR ADMIN:
1. Run PowerShell as Administrator and execute:
   .\jsn-to-txt-v2.ps1 -JsonFilePath "path\to\audit-results.json"

2. USAGE EXAMPLES:
   # Basic usage - output to same directory as input
   .\jsn-to-txt-v2.ps1 -JsonFilePath "C:\audits\exchange-results.json"

   # Specify custom output file
   .\jsn-to-txt-v2.ps1 -JsonFilePath "audit.json" -OutputFile "C:\reports\my-report.txt"

   # Use output directory (filename auto-generated)
   .\jsn-to-txt-v2.ps1 -JsonFilePath "audit.json" -OutputDirectory "C:\reports"

   # Disable timestamp in filename
   .\jsn-to-txt-v2.ps1 -JsonFilePath "audit.json" -IncludeTimestamp:$false

3. Review the generated comprehensive text report

CRITICAL: This script is 100% READ-ONLY and safe for production environments
All operations are file processing only - no Exchange modifications performed
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true, Position = 0, HelpMessage = "Path to the JSON audit file to convert")]
    [ValidateScript({
        if (-not (Test-Path $_)) {
            throw "JSON file not found: $_"
        }
        if (-not ($_ -like "*.json")) {
            throw "File must have .json extension: $_"
        }
        return $true
    })]
    [string]$JsonFilePath,

    [Parameter(Mandatory = $false, HelpMessage = "Full path for the output TXT file")]
    [string]$OutputFile,

    [Parameter(Mandatory = $false, HelpMessage = "Directory for output files (used when OutputFile not specified)")]
    [string]$OutputDirectory,

    [Parameter(Mandatory = $false, HelpMessage = "Include timestamp in generated filenames")]
    [switch]$IncludeTimestamp = $true
)

# ================================================================================
# SCRIPT INITIALIZATION AND VALIDATION
# ================================================================================

$ScriptStartTime = Get-Date
$ScriptVersion = "2.0.0"

# ================================================================================
# PARAMETER PROCESSING AND VALIDATION
# ================================================================================

# Resolve full path for input file
$JsonFilePath = Resolve-Path $JsonFilePath -ErrorAction Stop

# Determine output file path
if ($OutputFile) {
    # User specified exact output file path
    $textReportFile = $OutputFile
    $outputDir = Split-Path $textReportFile -Parent

    # Create output directory if it doesn't exist
    if (-not (Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        Write-Host "[INFO] Created output directory: $outputDir" -ForegroundColor Cyan
    }
} else {
    # Generate output file path based on input file
    $inputFileName = [System.IO.Path]::GetFileNameWithoutExtension($JsonFilePath)
    $inputDirectory = Split-Path $JsonFilePath -Parent

    if ($OutputDirectory) {
        # Use specified output directory
        $outputDir = $OutputDirectory
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
            Write-Host "[INFO] Created output directory: $outputDir" -ForegroundColor Cyan
        }
    } else {
        # Use same directory as input file
        $outputDir = $inputDirectory
    }

    # Generate filename with optional timestamp
    if ($IncludeTimestamp) {
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        $outputFileName = "$inputFileName-$timestamp.txt"
    } else {
        $outputFileName = "$inputFileName.txt"
    }

    $textReportFile = Join-Path $outputDir $outputFileName
}

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit JSON to TXT Converter" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Script Version: $ScriptVersion" -ForegroundColor White
Write-Host "Conversion Time: $($ScriptStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor White
Write-Host "Input File: $JsonFilePath" -ForegroundColor White
Write-Host "Output File: $textReportFile" -ForegroundColor White
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# ================================================================================
# HELPER FUNCTIONS
# ================================================================================

function Get-SafeFileName {
    param(
        [string]$BaseName,
        [string]$Extension,
        [string]$AuditID = "",
        [bool]$IncludeTimestamp = $true
    )

    $timestamp = if ($IncludeTimestamp) { Get-Date -Format "yyyyMMdd-HHmmss" } else { "" }
    $auditPart = if ($AuditID) { $AuditID.Substring(0, [Math]::Min(8, $AuditID.Length)) } else { "" }

    $parts = @($BaseName, $auditPart, $timestamp) | Where-Object { $_ -ne "" }
    $fileName = ($parts -join "-") + $Extension

    return $fileName
}

function Format-PermissionDetails {
    param(
        [Parameter(Mandatory = $true)]
        $PermissionArray,
        [string]$PermissionType = "Permission"
    )

    $details = @()

    if ($null -eq $PermissionArray -or $PermissionArray.Count -eq 0) {
        $details += "    No $PermissionType entries found."
        return $details
    }

    $counter = 1
    foreach ($permission in $PermissionArray) {
        $details += "    [$counter] $PermissionType Entry:"

        # Common fields for all permission types
        if ($permission.MailboxDisplayName) {
            $details += "        Mailbox: $($permission.MailboxDisplayName)"
        }
        if ($permission.MailboxPrimarySmtpAddress) {
            $details += "        Email: $($permission.MailboxPrimarySmtpAddress)"
        }
        if ($permission.MailboxType) {
            $details += "        Type: $($permission.MailboxType)"
        }
        if ($permission.Department) {
            $details += "        Department: $($permission.Department)"
        }
        if ($permission.User) {
            $details += "        User Account: $($permission.User)"
        }
        if ($permission.UserDisplayName) {
            $details += "        User Name: $($permission.UserDisplayName)"
        }
        if ($permission.UserDepartment) {
            $details += "        User Department: $($permission.UserDepartment)"
        }
        if ($permission.DelegateUser) {
            $details += "        Delegate User: $($permission.DelegateUser)"
        }
        if ($permission.DelegateUserDisplayName) {
            $details += "        Delegate Name: $($permission.DelegateUserDisplayName)"
        }
        if ($permission.DelegateUserDepartment) {
            $details += "        Delegate Department: $($permission.DelegateUserDepartment)"
        }
        if ($permission.AccessRights) {
            $details += "        Access Rights: $($permission.AccessRights)"
        }
        if ($permission.IsInherited -ne $null) {
            $details += "        Inherited: $($permission.IsInherited)"
        }
        if ($permission.AssignmentDate) {
            $details += "        Assignment Date: $($permission.AssignmentDate)"
        }
        if ($permission.LastAccessDate) {
            $details += "        Last Access: $($permission.LastAccessDate)"
        }
        if ($permission.BusinessJustification) {
            $details += "        Business Justification: $($permission.BusinessJustification)"
        }

        # Generate human-readable description and summary lines
        $humanReadable = Get-HumanReadablePermissionDescription -Permission $permission -PermissionType $PermissionType
        $summary = Get-PermissionSummaryLine -Permission $permission -PermissionType $PermissionType

        if ($humanReadable) {
            $details += "        Description: $humanReadable"
        }
        if ($summary) {
            $details += "        Summary: $summary"
        }

        # Add Action Plan section
        $details += ""
        $details += "        Action Plan: "
        $details += ""

        $details += ""
        $counter++
    }

    return $details
}

function Get-HumanReadablePermissionDescription {
    param(
        [Parameter(Mandatory = $true)]
        $Permission,
        [string]$PermissionType = "Permission"
    )

    # Determine user/assignee name
    $userName = ""
    if ($Permission.UserDisplayName) {
        $userName = $Permission.UserDisplayName
    } elseif ($Permission.DelegateUserDisplayName) {
        $userName = $Permission.DelegateUserDisplayName
    } elseif ($Permission.User) {
        $userName = $Permission.User -replace ".*\\", ""
    } elseif ($Permission.DelegateUser) {
        $userName = $Permission.DelegateUser -replace ".*\\", ""
    } else {
        $userName = "Unknown User"
    }

    # Determine target mailbox
    $targetMailbox = ""
    if ($Permission.MailboxDisplayName) {
        $targetMailbox = $Permission.MailboxDisplayName
        if ($Permission.MailboxPrimarySmtpAddress) {
            $targetMailbox += " ($($Permission.MailboxPrimarySmtpAddress))"
        }
    } else {
        $targetMailbox = "Unknown Mailbox"
    }

    # Generate description based on permission type
    switch ($PermissionType.ToLower()) {
        "full access" {
            return "$userName has full access to $targetMailbox"
        }
        "send-as" {
            return "$userName can send as $targetMailbox"
        }
        "send-on-behalf" {
            return "$userName can send on behalf of $targetMailbox"
        }
        default {
            $accessRights = if ($Permission.AccessRights) { $Permission.AccessRights } else { $PermissionType }
            return "$userName has $accessRights permission on $targetMailbox"
        }
    }
}

function Get-PermissionSummaryLine {
    param(
        [Parameter(Mandatory = $true)]
        $Permission,
        [string]$PermissionType = "Permission"
    )

    $summaryParts = @()

    # Account/User Type
    if ($Permission.MailboxType) {
        $summaryParts += "Mailbox Type: $($Permission.MailboxType)"
    }
    if ($Permission.UserDepartment) {
        $summaryParts += "User Dept: $($Permission.UserDepartment)"
    } elseif ($Permission.DelegateUserDepartment) {
        $summaryParts += "Delegate Dept: $($Permission.DelegateUserDepartment)"
    }

    # Assignment details
    if ($Permission.IsInherited -ne $null) {
        $inheritanceStatus = if ($Permission.IsInherited) { "Inherited" } else { "Direct" }
        $summaryParts += "Assignment: $inheritanceStatus"
    }

    # Dates
    if ($Permission.AssignmentDate) {
        $summaryParts += "Assigned: $($Permission.AssignmentDate)"
    }
    if ($Permission.LastAccessDate) {
        $summaryParts += "Last Access: $($Permission.LastAccessDate)"
    }

    # Access Rights
    if ($Permission.AccessRights) {
        $summaryParts += "Rights: $($Permission.AccessRights)"
    }

    # Status Fields - Enhanced with comprehensive status information
    # Configuration Status
    if ($Permission.IsValid -ne $null) {
        $configStatus = if ($Permission.IsValid) { "Valid" } else { "Invalid" }
        $summaryParts += "Config: $configStatus"
    }

    # Active Status
    if ($Permission.IsEnabled -ne $null) {
        $activeStatus = if ($Permission.IsEnabled) { "Enabled" } else { "Disabled" }
        $summaryParts += "Status: $activeStatus"
    }

    # Audit Status
    if ($Permission.AuditEnabled -ne $null) {
        $auditStatus = if ($Permission.AuditEnabled) { "Enabled" } else { "Disabled" }
        $summaryParts += "Audit: $auditStatus"
    }

    # Bypass Status
    if ($Permission.AuditBypassEnabled -ne $null) {
        $bypassStatus = if ($Permission.AuditBypassEnabled) { "Yes" } else { "No" }
        $summaryParts += "Bypass: $bypassStatus"
    }

    # Compliance Status
    if ($Permission.ComplianceStatus) {
        $summaryParts += "Compliance: $($Permission.ComplianceStatus)"
    }

    # Business justification indicator
    if ($Permission.BusinessJustification) {
        $summaryParts += "Justified: Yes"
    } else {
        $summaryParts += "Justified: No"
    }

    return $summaryParts -join " | "
}

function Get-HumanReadableImpersonationDescription {
    param(
        [Parameter(Mandatory = $true)]
        $User
    )

    # Determine assignee name
    $assigneeName = ""
    if ($User.RoleAssigneeName) {
        $assigneeName = $User.RoleAssigneeName
    } elseif ($User.RoleAssignee) {
        $assigneeName = $User.RoleAssignee -replace ".*\\", "" -replace ".*/", ""
    } else {
        $assigneeName = "Unknown Assignee"
    }

    return "$assigneeName has ApplicationImpersonation rights across All Mailboxes (Organization-Wide)"
}

function Get-ImpersonationSummaryLine {
    param(
        [Parameter(Mandatory = $true)]
        $User
    )

    $summaryParts = @()

    # Account Type
    if ($User.RoleAssigneeType) {
        $summaryParts += "Account Type: $($User.RoleAssigneeType)"
    }

    # Assignment Method
    if ($User.AssignmentMethod) {
        $summaryParts += "Assignment Method: $($User.AssignmentMethod)"
    }

    # Creation Date
    if ($User.WhenCreated) {
        $summaryParts += "Created: $($User.WhenCreated)"
    }

    # Status Fields - Enhanced with comprehensive status information
    # Configuration Status
    if ($User.IsValid -ne $null) {
        $configStatus = if ($User.IsValid) { "Valid" } else { "Invalid" }
        $summaryParts += "Config: $configStatus"
    }

    # Active Status
    if ($User.IsEnabled -ne $null) {
        $activeStatus = if ($User.IsEnabled) { "Enabled" } else { "Disabled" }
        $summaryParts += "Status: $activeStatus"
    }

    # Audit Status
    if ($User.AuditEnabled -ne $null) {
        $auditStatus = if ($User.AuditEnabled) { "Enabled" } else { "Disabled" }
        $summaryParts += "Audit: $auditStatus"
    } elseif ($User.AuditLoggingEnabled -ne $null) {
        $auditStatus = if ($User.AuditLoggingEnabled) { "Enabled" } else { "Disabled" }
        $summaryParts += "Audit: $auditStatus"
    }

    # Bypass Status
    if ($User.AuditBypassEnabled -ne $null) {
        $bypassStatus = if ($User.AuditBypassEnabled) { "Yes" } else { "No" }
        $summaryParts += "Bypass: $bypassStatus"
    }

    # Compliance Status
    if ($User.ComplianceStatus) {
        $summaryParts += "Compliance: $($User.ComplianceStatus)"
    }

    return $summaryParts -join " | "
}

function Format-CrossDomainAnalysis {
    param(
        [Parameter(Mandatory = $true)]
        $CrossDomainData
    )

    $details = @()

    if ($null -eq $CrossDomainData) {
        return $details
    }

    $details += "CROSS-DOMAIN ANALYSIS:"
    $details += ""

    # Cross-Domain Relationships
    if ($CrossDomainData.CrossDomainRelationships -and $CrossDomainData.CrossDomainRelationships.Count -gt 0) {
        $details += "  CROSS-DOMAIN RELATIONSHIPS:"
        $counter = 1
        foreach ($relationship in $CrossDomainData.CrossDomainRelationships) {
            $details += "    [$counter] Cross-Domain Permission:"
            $details += "        Mailbox: $($relationship.MailboxDisplayName)"
            $details += "        Mailbox Email: $($relationship.MailboxPrimarySmtpAddress)"
            $details += "        Mailbox Domain: $($relationship.MailboxDomain)"
            $details += "        Admin User: $($relationship.AdminUser)"
            $details += "        Admin Domain: $($relationship.AdminDomain)"
            $details += "        Admin Classification: $($relationship.AdminClassification)"
            $details += "        Access Rights: $($relationship.AccessRights)"
            $details += "        Is Cross-Domain: $($relationship.IsCrossDomain)"
            $details += "        Risk Score: $($relationship.RiskScore)"
            $details += "        Risk Level: $($relationship.RiskLevel)"
            $details += "        Permission Type: $($relationship.PermissionType)"
            $details += "        Inherited: $($relationship.IsInherited)"

            # Add human-readable description for cross-domain relationship
            $adminUserName = $relationship.AdminUser -replace ".*\\", ""
            $mailboxName = $relationship.MailboxDisplayName
            $adminDomain = $relationship.AdminDomain
            $mailboxDomain = $relationship.MailboxDomain
            $accessType = $relationship.AccessRights
            $riskLevel = $relationship.RiskLevel

            $securityImplication = ""
            switch ($riskLevel.ToLower()) {
                "high" { $securityImplication = "This represents a HIGH SECURITY RISK as it allows cross-domain administrative access." }
                "medium" { $securityImplication = "This represents a MODERATE SECURITY RISK requiring review and justification." }
                "low" { $securityImplication = "This represents a LOW SECURITY RISK but should still be monitored." }
                default { $securityImplication = "This cross-domain relationship requires security assessment." }
            }

            $details += "        Description: User '$adminUserName' from domain '$adminDomain' has $accessType access to mailbox '$mailboxName' in domain '$mailboxDomain'. $securityImplication"
            $details += "        Summary: Cross-Domain: $adminDomain → $mailboxDomain | Access: $accessType | Risk: $riskLevel | Classification: $($relationship.AdminClassification)"

            $details += ""
            $details += "        Action Plan: "
            $details += ""

            $details += ""
            $counter++
        }
    }

    # Permission Summary
    if ($CrossDomainData.PermissionSummary) {
        $details += "  PERMISSION SUMMARY:"
        foreach ($summaryKey in $CrossDomainData.PermissionSummary.PSObject.Properties.Name) {
            $summary = $CrossDomainData.PermissionSummary.$summaryKey
            $details += "    Domain Relationship: $summaryKey"
            $details += "      Total Count: $($summary.Count)"
            $details += "      Admin Domain: $($summary.AdminDomain)"
            $details += "      Mailbox Domain: $($summary.MailboxDomain)"
            $details += "      High Risk Count: $($summary.HighRiskCount)"
            if ($summary.AdminTypes) {
                $details += "      Admin Types:"
                foreach ($adminType in $summary.AdminTypes.PSObject.Properties.Name) {
                    $details += "        $adminType`: $($summary.AdminTypes.$adminType)"
                }
            }
            $details += ""
        }
    }

    # Risk Assessment
    if ($CrossDomainData.RiskAssessment) {
        $risk = $CrossDomainData.RiskAssessment
        $details += "  RISK ASSESSMENT:"
        $details += "    Total Permission Relationships: $($risk.TotalPermissionRelationships)"
        $details += "    Cross-Domain Relationships: $($risk.CrossDomainRelationships)"
        $details += "    High Risk Relationships: $($risk.HighRiskRelationships)"
        $details += "    Cross-Domain Percentage: $($risk.CrossDomainPercentage)%"
        if ($risk.RiskDistribution) {
            $details += "    Risk Distribution:"
            $details += "      High: $($risk.RiskDistribution.High)"
            $details += "      Medium: $($risk.RiskDistribution.Medium)"
            $details += "      Low: $($risk.RiskDistribution.Low)"
        }
        $details += ""
    }

    return $details
}

function Format-AdministratorDiscovery {
    param(
        [Parameter(Mandatory = $true)]
        $AdminData
    )

    $details = @()

    if ($null -eq $AdminData) {
        return $details
    }

    $details += "================================================================================"
    $details += "ADMINISTRATOR DISCOVERY"
    $details += "================================================================================"
    $details += ""

    # Role Assignments
    if ($AdminData.RoleAssignments -and $AdminData.RoleAssignments.Count -gt 0) {
        $details += "EXCHANGE ROLE ASSIGNMENTS:"
        $details += "  Total Role Assignments: $($AdminData.RoleAssignments.Count)"
        $details += ""

        $counter = 1
        foreach ($assignment in $AdminData.RoleAssignments) {
            $details += "  [$counter] Role Assignment:"
            $details += "      Role Assignee: $($assignment.RoleAssignee)"
            $details += "      Assignee Domain: $($assignment.AssigneeDomain)"
            $details += "      Assignee Type: $($assignment.AssigneeType)"
            $details += "      Role: $($assignment.Role)"
            $details += "      Role Type: $($assignment.RoleType)"
            if ($assignment.Scope) {
                $details += "      Scope Type: $($assignment.Scope.Type)"
                if ($assignment.Scope.AffectedDomains) {
                    $details += "      Affected Domains: $($assignment.Scope.AffectedDomains -join ', ')"
                }
                $details += "      Organization-Wide: $($assignment.Scope.IsOrganizationWide)"
            }
            $details += "      Is Enabled: $($assignment.IsEnabled)"
            $details += "      Admin Classification: $($assignment.AdminClassification)"
            $details += "      Assignment Method: $($assignment.AssignmentMethod)"
            $details += ""
            $counter++
        }
    }

    # Admin Domain Map
    if ($AdminData.AdminDomainMap) {
        $details += "ADMINISTRATOR DOMAIN MAPPING:"
        foreach ($domain in $AdminData.AdminDomainMap.PSObject.Properties.Name) {
            $admins = $AdminData.AdminDomainMap.$domain
            $details += "  Domain: $domain"
            $details += "    Administrator Count: $($admins.Count)"
            foreach ($admin in $admins) {
                $details += "      - $($admin.RoleAssignee) ($($admin.AdminClassification))"
            }
            $details += ""
        }
    }

    # Cross-Domain Relationships
    if ($AdminData.CrossDomainRelationships -and $AdminData.CrossDomainRelationships.Count -gt 0) {
        $details += "CROSS-DOMAIN ADMINISTRATIVE RELATIONSHIPS:"
        $counter = 1
        foreach ($relationship in $AdminData.CrossDomainRelationships) {
            $details += "  [$counter] Cross-Domain Admin Relationship:"
            $details += "      Admin Domain: $($relationship.AdminDomain)"
            $details += "      Target Domain: $($relationship.TargetDomain)"
            $details += "      Relationship Type: $($relationship.RelationshipType)"
            $details += "      Risk Level: $($relationship.RiskLevel)"
            $details += "      Admin Count: $($relationship.AdminCount)"
            $details += ""
            $counter++
        }
    }

    # Admin Classification
    if ($AdminData.AdminClassification) {
        $details += "ADMINISTRATOR CLASSIFICATION SUMMARY:"
        $classificationCounts = @{}
        foreach ($admin in $AdminData.AdminClassification.PSObject.Properties.Name) {
            $classification = $AdminData.AdminClassification.$admin
            if ($classificationCounts.ContainsKey($classification)) {
                $classificationCounts[$classification]++
            } else {
                $classificationCounts[$classification] = 1
            }
            $details += "  $admin`: $classification"
        }
        $details += ""
        $details += "CLASSIFICATION SUMMARY:"
        foreach ($classification in $classificationCounts.Keys) {
            $details += "  $classification`: $($classificationCounts[$classification]) administrators"
        }
        $details += ""
    }

    return $details
}

# ================================================================================
# MAIN PROCESSING
# ================================================================================

Write-Host "Step 1/2: Loading and parsing JSON audit data..." -ForegroundColor Yellow

try {
    $jsonContent = Get-Content -Path $JsonFilePath -Raw -Encoding UTF8
    $auditData = $jsonContent | ConvertFrom-Json
    Write-Host "[SUCCESS] JSON data loaded successfully" -ForegroundColor Green

    # Extract audit metadata for file naming
    $auditID = if ($auditData.AuditMetadata -and $auditData.AuditMetadata.AuditID) {
        $auditData.AuditMetadata.AuditID
    } else {
        [System.Guid]::NewGuid().ToString().Substring(0, 8)
    }

    Write-Host "  Audit ID: $auditID" -ForegroundColor Gray
    Write-Host "  Organization: $($auditData.AuditMetadata.OrganizationName)" -ForegroundColor Gray
    Write-Host "  Audit Date: $($auditData.AuditMetadata.AuditStartTime)" -ForegroundColor Gray

} catch {
    Write-Host "[ERROR] Failed to load JSON data: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Step 2/2: Generating comprehensive text report..." -ForegroundColor Yellow

# ================================================================================
# TEXT REPORT GENERATION
# ================================================================================

try {
    $report = @()

    # Report Header
    $report += "================================================================================"
    $report += "EXCHANGE MAILBOX SECURITY AUDIT REPORT"
    $report += "================================================================================"
    $report += ""
    $report += "Report Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    $report += "Source JSON File: $(Split-Path $JsonFilePath -Leaf)"
    $report += "Conversion Script: Exchange JSON to TXT Converter v$ScriptVersion"
    $report += "Created By: E.Z. Consultancy"
    $report += ""

    # Audit Metadata Section - Fixed field name mappings
    if ($auditData.AuditMetadata) {
        $metadata = $auditData.AuditMetadata
        $report += "================================================================================"
        $report += "AUDIT METADATA"
        $report += "================================================================================"
        $report += ""
        $report += "Audit ID: $($metadata.AuditID)"
        $report += "Organization: $($metadata.OrganizationName)"
        $report += "Exchange Version: $($metadata.ExchangeVersion)"
        $report += "Audit Start Time: $($metadata.AuditStartTime)"
        $report += "Audit End Time: $($metadata.AuditEndTime)"
        $report += "Audit Duration: $($metadata.AuditDuration)"
        $report += "Script Version: $($metadata.ScriptVersion)"
        $report += "Script Release Date: $($metadata.ScriptReleaseDate)"
        $report += "Script Author: $($metadata.ScriptAuthor)"
        $report += "PowerShell Version: $($metadata.PowerShellVersion)"
        $report += "PowerShell Edition: $($metadata.PowerShellEdition)"
        $report += "Executed By: $($metadata.AuditExecutedBy)"
        $report += "Execution Host: $($metadata.ComputerName)"
        $report += "Total Mailboxes: $($metadata.TotalMailboxes)"
        $report += "Max Mailbox Records: $($metadata.MaxMailboxSample)"
        $report += "Audit Scope: $($metadata.AssessmentScope)"
        $report += "Audit User: $($metadata.AuditUser)"
        $report += "Domain Filter Enabled: $($metadata.DomainFilterEnabled)"
        if ($metadata.FilteredDomains -and $metadata.FilteredDomains.Count -gt 0) {
            $report += "Filtered Domains: $($metadata.FilteredDomains -join ', ')"
        }
        $report += "Domain Filter Scope: $($metadata.DomainFilterScope)"
        $report += "Exchange Servers: $($metadata.ExchangeServers)"
        $report += ""
    }

    # Executive Summary
    if ($auditData.ComplianceSummary) {
        $summary = $auditData.ComplianceSummary

        # Calculate compliance metrics from individual controls
        $allControls = @($auditData.MBX_1_1_ImpersonationRights, $auditData.MBX_2_1_FullAccessPermissions,
                        $auditData.MBX_3_1_AuditLogging, $auditData.MBX_4_1_SendAsPermissions,
                        $auditData.MBX_5_1_SendOnBehalfPermissions) | Where-Object { $_ -ne $null }

        $totalControls = $allControls.Count

        # Count compliance statuses manually to avoid filtering issues
        $compliantControls = 0
        $reviewRequiredControls = 0
        $nonCompliantControls = 0
        $noDataControls = 0

        foreach ($control in $allControls) {
            switch ($control.ComplianceStatus) {
                "Compliant" { $compliantControls++ }
                "Review Required" { $reviewRequiredControls++ }
                "Non-Compliant" { $nonCompliantControls++ }
                "No Data" { $noDataControls++ }
            }
        }

        # Calculate overall compliance score
        $totalComplianceScore = ($allControls | ForEach-Object {
            $score = $_.ComplianceScore
            if ($score -is [string]) { [int]$score } else { $score }
        } | Measure-Object -Sum).Sum
        $overallComplianceScore = if ($totalControls -gt 0) { [math]::Round($totalComplianceScore / $totalControls, 1) } else { 0 }

        # Calculate assessment success rate
        $successfulAssessments = if ($summary.SuccessfulAssessments) { $summary.SuccessfulAssessments } else { $totalControls - $noDataControls }
        $assessmentSuccessRate = if ($totalControls -gt 0) { [math]::Round(($successfulAssessments / $totalControls) * 100, 1) } else { 0 }

        # Count risk levels manually to avoid filtering issues
        $criticalFindings = 0
        $highRiskFindings = 0
        $mediumRiskFindings = 0
        $lowRiskFindings = 0

        foreach ($control in $allControls) {
            switch ($control.RiskLevel) {
                "Critical" { $criticalFindings++ }
                "High" { $highRiskFindings++ }
                "Medium" { $mediumRiskFindings++ }
                "Low" { $lowRiskFindings++ }
            }
        }

        $report += "================================================================================"
        $report += "EXECUTIVE SUMMARY"
        $report += "================================================================================"
        $report += ""
        $report += "Overall Compliance Score: $overallComplianceScore%"
        $report += "Assessment Success Rate: $assessmentSuccessRate%"
        $report += ""
        $report += "CONTROL ASSESSMENT SUMMARY:"
        $report += "  Total Controls Assessed: $totalControls"
        $report += "  Controls Compliant: $compliantControls"
        $report += "  Controls Requiring Review: $reviewRequiredControls"
        $report += "  Controls Non-Compliant: $nonCompliantControls"
        $report += "  Controls with No Data: $noDataControls"
        $report += ""
        $report += "RISK ASSESSMENT SUMMARY:"
        $report += "  Critical Findings: $criticalFindings"
        $report += "  High Risk Findings: $highRiskFindings"
        $report += "  Medium Risk Findings: $mediumRiskFindings"
        $report += "  Low Risk Findings: $lowRiskFindings"
        $report += ""
        $report += "AUDIT EXECUTION SUMMARY:"
        $report += "  Successful Assessments: $($summary.SuccessfulAssessments)"
        $report += "  Failed Assessments: $($summary.FailedAssessments)"
        $report += "  Audit Duration: $($summary.AuditDurationSeconds) seconds"
        $report += "  Assessment Type: $($summary.AssessmentType)"
        $report += ""
    }

    # Security Controls Assessment - Enhanced with Complete Details
    $controls = @(
        @{ Key = "MBX_1_1_ImpersonationRights"; Title = "MAILBOX IMPERSONATION RIGHTS (MBX-1.1)" },
        @{ Key = "MBX_2_1_FullAccessPermissions"; Title = "MAILBOX FULL ACCESS PERMISSIONS (MBX-2.1)" },
        @{ Key = "MBX_3_1_AuditLogging"; Title = "MAILBOX AUDIT LOGGING CONFIGURATION (MBX-3.1)" },
        @{ Key = "MBX_4_1_SendAsPermissions"; Title = "SEND-AS PERMISSIONS (MBX-4.1)" },
        @{ Key = "MBX_5_1_SendOnBehalfPermissions"; Title = "SEND-ON-BEHALF PERMISSIONS (MBX-5.1)" }
    )

    foreach ($control in $controls) {
        $controlData = $auditData.$($control.Key)
        if ($null -eq $controlData) { continue }

        $report += "================================================================================"
        $report += $control.Title
        $report += "================================================================================"
        $report += ""
        $report += "Control ID: $($controlData.ControlID)"
        $report += "Risk Level: $($controlData.RiskLevel)"
        $report += "CVSS Score: $($controlData.CVSSScore)"
        $report += "Compliance Status: $($controlData.ComplianceStatus)"
        $report += "Compliance Score: $($controlData.ComplianceScore)%"
        $report += "Records Analyzed: $($controlData.SampleSize)"
        $report += ""
        $report += "FINDING:"
        $report += "  $($controlData.Finding)"
        $report += ""
        $report += "RECOMMENDATION:"
        $report += "  $($controlData.Recommendation)"
        $report += ""

        # Add comprehensive details for each control type
        switch ($control.Key) {
            "MBX_1_1_ImpersonationRights" {
                $report += "IMPERSONATION RIGHTS COMPREHENSIVE DETAILS:"
                $report += "  Total Impersonation Assignments: $($controlData.TotalImpersonationAssignments)"
                $report += ""
                if ($controlData.ImpersonationUsers -and $controlData.ImpersonationUsers.Count -gt 0) {
                    $report += "  COMPLETE IMPERSONATION RIGHTS LISTING:"
                    $counter = 1
                    foreach ($user in $controlData.ImpersonationUsers) {
                        # Create enhanced user object with additional status information
                        $enhancedUser = @{}
                        # Copy all original properties
                        $user.PSObject.Properties | ForEach-Object {
                            $enhancedUser[$_.Name] = $_.Value
                        }

                        # Look for matching role assignment in Administrator Discovery data
                        if ($auditData.MBX_5_1_AdministratorDiscovery -and $auditData.MBX_5_1_AdministratorDiscovery.RoleAssignments) {
                            # Try exact match first
                            $matchingAdmin = $auditData.MBX_5_1_AdministratorDiscovery.RoleAssignments | Where-Object {
                                $_.RoleAssignee -eq $user.RoleAssignee
                            }

                            # If no exact match, try matching by username only (after the backslash)
                            if (-not $matchingAdmin) {
                                $userName = $user.RoleAssignee -replace '.*\\', ''
                                $matchingAdmin = $auditData.MBX_5_1_AdministratorDiscovery.RoleAssignments | Where-Object {
                                    ($_.RoleAssignee -replace '.*\\', '') -eq $userName
                                }
                            }

                            if ($matchingAdmin) {
                                # Add additional status fields from Administrator Discovery
                                $enhancedUser["IsEnabled"] = $matchingAdmin.IsEnabled
                                $enhancedUser["AdminClassification"] = $matchingAdmin.AdminClassification
                            }
                        }

                        # Look for audit information from Mailbox Audit data
                        if ($auditData.MBX_4_1_AuditLogging) {
                            # Check for audit bypass status
                            if ($auditData.MBX_4_1_AuditLogging.MailboxAuditBypassUsers) {
                                $bypassUser = $auditData.MBX_4_1_AuditLogging.MailboxAuditBypassUsers | Where-Object {
                                    $_.Name -eq ($user.RoleAssignee -replace ".*\\", "")
                                }
                                if ($bypassUser) {
                                    $enhancedUser["AuditBypassEnabled"] = $bypassUser.AuditBypassEnabled
                                }
                            }

                            # Check for general audit logging status
                            if ($auditData.MBX_4_1_AuditLogging.AdminAuditLogEnabled -ne $null) {
                                $enhancedUser["AuditLoggingEnabled"] = $auditData.MBX_4_1_AuditLogging.AdminAuditLogEnabled
                            }
                        }

                        # Convert hashtable back to PSObject for compatibility with helper functions
                        $enhancedUserObj = New-Object PSObject
                        $enhancedUser.GetEnumerator() | ForEach-Object {
                            $enhancedUserObj | Add-Member -NotePropertyName $_.Key -NotePropertyValue $_.Value
                        }

                        $report += "    [$counter] Impersonation Assignment:"
                        $report += "        Role Assignee: $($enhancedUserObj.RoleAssigneeName)"
                        $report += "        Full DN: $($enhancedUserObj.RoleAssignee)"
                        $report += "        Assignee Type: $($enhancedUserObj.RoleAssigneeType)"
                        $report += "        Assignment Method: $($enhancedUserObj.AssignmentMethod)"
                        $report += "        When Created: $($enhancedUserObj.WhenCreated)"
                        $report += "        Is Valid: $($enhancedUserObj.IsValid)"
                        # Enhanced status fields
                        if ($enhancedUserObj.IsEnabled -ne $null) {
                            $report += "        Is Enabled: $($enhancedUserObj.IsEnabled)"
                        }
                        if ($enhancedUserObj.AdminClassification) {
                            $report += "        Admin Classification: $($enhancedUserObj.AdminClassification)"
                        }
                        if ($enhancedUserObj.AuditLoggingEnabled -ne $null) {
                            $auditStatus = if ($enhancedUserObj.AuditLoggingEnabled) { "Enabled" } else { "Disabled" }
                            $report += "        Audit Logging: $auditStatus"
                        }
                        if ($enhancedUserObj.AuditBypassEnabled -ne $null) {
                            $bypassStatus = if ($enhancedUserObj.AuditBypassEnabled) { "Yes" } else { "No" }
                            $report += "        Audit Bypass: $bypassStatus"
                        }

                        # Generate human-readable description and summary for impersonation rights using enhanced data
                        $humanReadable = Get-HumanReadableImpersonationDescription -User $enhancedUserObj
                        $summary = Get-ImpersonationSummaryLine -User $enhancedUserObj

                        if ($humanReadable) {
                            $report += "        Description: $humanReadable"
                        }
                        if ($summary) {
                            $report += "        Summary: $summary"
                        }

                        # Add Action Plan section
                        $report += ""
                        $report += "        Action Plan: "
                        $report += ""

                        $report += ""
                        $counter++
                    }
                } else {
                    $report += "  No impersonation rights assignments found."
                }
            }
            "MBX_2_1_FullAccessPermissions" {
                $report += "FULL ACCESS PERMISSIONS COMPREHENSIVE DETAILS:"
                $report += "  Total Full Access Permissions: $($controlData.TotalFullAccessPermissions)"
                $report += "  Unique Users with Full Access: $($controlData.UniqueUsersWithFullAccess)"
                $report += ""
                if ($controlData.FullAccessPermissions -and $controlData.FullAccessPermissions.Count -gt 0) {
                    $report += "  COMPLETE FULL ACCESS PERMISSIONS LISTING:"
                    $report += Format-PermissionDetails -PermissionArray $controlData.FullAccessPermissions -PermissionType "Full Access"
                } else {
                    $report += "  No full access permissions found."
                }

                # Add Cross-Domain Analysis if present
                if ($controlData.CrossDomainAnalysis) {
                    $report += ""
                    $report += Format-CrossDomainAnalysis -CrossDomainData $controlData.CrossDomainAnalysis
                }
            }
            "MBX_3_1_AuditLogging" {
                $report += "AUDIT LOGGING COMPREHENSIVE DETAILS:"
                $report += "  Admin Audit Logging Enabled: $($controlData.AdminAuditLogEnabled)"
                $report += "  Mailboxes with Audit Enabled: $($controlData.AuditEnabledMailboxes)"
                $report += "  Audit Bypass Users Count: $($controlData.AuditBypassCount)"
                $report += "  Records Analyzed: $($controlData.SampleSize)"
                $report += ""

                # Add audit bypass users details - Fixed array processing
                if ($controlData.MailboxAuditBypassUsers -and $controlData.MailboxAuditBypassUsers.Count -gt 0) {
                    $report += "  MAILBOX AUDIT BYPASS USERS:"
                    $counter = 1
                    foreach ($bypassUser in $controlData.MailboxAuditBypassUsers) {
                        if ($bypassUser -and $bypassUser -ne "" -and $bypassUser -ne $null) {
                            # Handle both string and object formats
                            if ($bypassUser -is [string]) {
                                $report += "    [$counter] User: $bypassUser"
                            } elseif ($bypassUser.Name) {
                                $bypassStatus = if ($bypassUser.AuditBypassEnabled) { "Enabled" } else { "Disabled" }
                                $report += "    [$counter] User: $($bypassUser.Name) (Bypass: $bypassStatus)"
                            } else {
                                $report += "    [$counter] User: $($bypassUser.ToString())"
                            }
                            $counter++
                        }
                    }
                    if ($counter -eq 1) {
                        $report += "    No bypass users configured (good security practice)"
                    }
                    $report += ""
                }

                # Add mailbox audit settings from audit data - Fixed array processing
                if ($controlData.SampleMailboxAuditSettings -and $controlData.SampleMailboxAuditSettings.Count -gt 0) {
                    $report += "  MAILBOX AUDIT SETTINGS:"
                    $report += "    Total Audited Mailboxes: $($controlData.SampleMailboxAuditSettings.Count)"
                    $report += ""

                    # Display all audited mailbox configurations
                    $counter = 1
                    foreach ($mailboxAudit in $controlData.SampleMailboxAuditSettings) {
                        $report += "    [$counter] Mailbox Audit Configuration:"
                        $report += "        Identity: $($mailboxAudit.Identity)"
                        $report += "        Display Name: $($mailboxAudit.DisplayName)"
                        $report += "        Audit Enabled: $($mailboxAudit.AuditEnabled)"
                        if ($mailboxAudit.AuditLogAgeLimit) {
                            $report += "        Audit Log Age Limit: $($mailboxAudit.AuditLogAgeLimit)"
                        }
                        if ($mailboxAudit.AuditOwner) {
                            $report += "        Audit Owner Actions: $($mailboxAudit.AuditOwner -join ', ')"
                        }
                        if ($mailboxAudit.AuditDelegate) {
                            $report += "        Audit Delegate Actions: $($mailboxAudit.AuditDelegate -join ', ')"
                        }
                        if ($mailboxAudit.AuditAdmin) {
                            $report += "        Audit Admin Actions: $($mailboxAudit.AuditAdmin -join ', ')"
                        }
                        $report += ""
                        $counter++
                    }
                } else {
                    $report += "  MAILBOX AUDIT SETTINGS:"
                    $report += "    No mailbox audit settings available"
                    $report += ""
                }

                # Add current assessment value
                if ($controlData.CurrentValue) {
                    $report += "  CURRENT ASSESSMENT:"
                    $report += "    $($controlData.CurrentValue)"
                    $report += ""
                }
            }
            "MBX_4_1_SendAsPermissions" {
                $report += "SEND-AS PERMISSIONS COMPREHENSIVE DETAILS:"
                $report += "  Total Send-As Permissions: $($controlData.TotalSendAsPermissions)"
                $report += "  Unique Users with Send-As: $($controlData.UniqueUsersWithSendAs)"
                $report += ""
                if ($controlData.SendAsPermissions -and $controlData.SendAsPermissions.Count -gt 0) {
                    $report += "  COMPLETE SEND-AS PERMISSIONS LISTING:"
                    $report += Format-PermissionDetails -PermissionArray $controlData.SendAsPermissions -PermissionType "Send-As"
                } else {
                    $report += "  No Send-As permissions found."
                }
            }
            "MBX_5_1_SendOnBehalfPermissions" {
                $report += "SEND-ON-BEHALF PERMISSIONS COMPREHENSIVE DETAILS:"
                $report += "  Total Send-On-Behalf Permissions: $($controlData.TotalSendOnBehalfPermissions)"
                $report += "  Unique Users with Send-On-Behalf: $($controlData.UniqueUsersWithSendOnBehalf)"
                $report += ""
                if ($controlData.SendOnBehalfPermissions -and $controlData.SendOnBehalfPermissions.Count -gt 0) {
                    $report += "  COMPLETE SEND-ON-BEHALF PERMISSIONS LISTING:"
                    $report += Format-PermissionDetails -PermissionArray $controlData.SendOnBehalfPermissions -PermissionType "Send-On-Behalf"
                } else {
                    $report += "  No Send-On-Behalf permissions found."
                }
            }
        }
        $report += ""
    }

    # Administrator Discovery Section - New comprehensive section
    if ($auditData.AdministratorDiscovery) {
        $report += Format-AdministratorDiscovery -AdminData $auditData.AdministratorDiscovery
    }

    # Report Footer
    $report += "================================================================================"
    $report += "END OF REPORT"
    $report += "================================================================================"
    $report += ""
    $report += "Report Generation Completed: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    $report += "Total Processing Time: $((Get-Date) - $ScriptStartTime)"
    $report += ""
    $report += "This comprehensive report contains all detailed audit information including:"
    $report += "  - Complete permission listings for all security controls"
    $report += "  - Full user and mailbox metadata for each permission assignment"
    $report += "  - Detailed audit logging configuration and compliance status"
    $report += "  - Business justifications and assignment dates where available"
    $report += "  - Department and organizational context for all findings"
    $report += ""
    $report += "Generated by: Exchange Audit Tools - E.Z. Consultancy"
    $report += "Script Version: $ScriptVersion"
    $report += "================================================================================"

    # Write report to file
    $report | Out-File -FilePath $textReportFile -Encoding UTF8
    Write-Host "[SUCCESS] Text report generated: $(Split-Path $textReportFile -Leaf)" -ForegroundColor Green

} catch {
    Write-Host "[ERROR] Failed to generate text report: $($_.Exception.Message)" -ForegroundColor Red
}

# ================================================================================
# COMPLETION SUMMARY
# ================================================================================

$ScriptEndTime = Get-Date
$ProcessingDuration = $ScriptEndTime - $ScriptStartTime

Write-Host ""
Write-Host "================================================================================" -ForegroundColor Green
Write-Host "JSON TO TXT CONVERSION COMPLETED" -ForegroundColor Green
Write-Host "================================================================================" -ForegroundColor Green
Write-Host "Processing Duration: $ProcessingDuration" -ForegroundColor White
Write-Host "Output File: $textReportFile" -ForegroundColor White
Write-Host ""
Write-Host "COMPREHENSIVE TEXT REPORT GENERATED:" -ForegroundColor White

# Display generated file information
try {
    if (Test-Path $textReportFile) {
        $fileInfo = Get-Item $textReportFile
        $fileSize = [math]::Round($fileInfo.Length / 1KB, 2)
        Write-Host "  - $($fileInfo.Name) ($fileSize KB)" -ForegroundColor Gray
        Write-Host "  - Full Path: $($fileInfo.FullName)" -ForegroundColor Gray
    } else {
        Write-Host "  [WARNING] Output file not found: $textReportFile" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  [WARNING] Could not access generated file: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "REPORT FEATURES:" -ForegroundColor Cyan
Write-Host "- Executive summary with compliance scores and risk assessments" -ForegroundColor White
Write-Host "- Complete audit metadata and environment information" -ForegroundColor White
Write-Host "- Comprehensive details for all security controls (MBX-1.1 through MBX-5.1)" -ForegroundColor White
Write-Host "- Full permission listings with all available metadata" -ForegroundColor White
Write-Host "- User details, assignment dates, and business justifications" -ForegroundColor White
Write-Host "- Department context and organizational information" -ForegroundColor White
Write-Host "- Professional formatting suitable for security analysts and auditors" -ForegroundColor White
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Review the comprehensive text report for all audit findings and details" -ForegroundColor White
Write-Host "2. Share the report with stakeholders based on their access level" -ForegroundColor White
Write-Host "3. Use the detailed permission listings for remediation planning" -ForegroundColor White
Write-Host "4. Reference the executive summary for high-level compliance reporting" -ForegroundColor White
Write-Host ""
Write-Host "================================================================================" -ForegroundColor Green
