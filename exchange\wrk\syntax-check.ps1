# PowerShell Syntax Checker
param(
    [Parameter(Mandatory = $true)]
    [string]$ScriptPath
)

try {
    Write-Host "Checking PowerShell syntax for: $ScriptPath" -ForegroundColor Cyan
    
    # Read the script content
    $scriptContent = Get-Content -Path $ScriptPath -Raw -ErrorAction Stop
    
    # Parse the script using AST
    $errors = $null
    $tokens = $null
    $ast = [System.Management.Automation.Language.Parser]::ParseInput($scriptContent, [ref]$tokens, [ref]$errors)
    
    if ($errors -and $errors.Count -gt 0) {
        Write-Host "SYNTAX ERRORS FOUND:" -ForegroundColor Red
        Write-Host "===================" -ForegroundColor Red
        
        foreach ($error in $errors) {
            Write-Host "Line $($error.Extent.StartLineNumber), Column $($error.Extent.StartColumnNumber): $($error.Message)" -ForegroundColor Red
            Write-Host "  Error ID: $($error.ErrorId)" -ForegroundColor Yellow
            Write-Host "  Severity: $($error.Severity)" -ForegroundColor Yellow
            Write-Host ""
        }
        
        Write-Host "Total errors found: $($errors.Count)" -ForegroundColor Red
    } else {
        Write-Host "NO SYNTAX ERRORS FOUND" -ForegroundColor Green
        Write-Host "Script appears to be syntactically correct." -ForegroundColor Green
    }
    
    # Additional checks
    Write-Host ""
    Write-Host "ADDITIONAL ANALYSIS:" -ForegroundColor Cyan
    Write-Host "===================" -ForegroundColor Cyan
    Write-Host "Total tokens: $($tokens.Count)" -ForegroundColor White
    Write-Host "AST nodes: $($ast.FindAll({$true}, $true).Count)" -ForegroundColor White
    
    # Check for common issues
    $openBraces = ($tokens | Where-Object { $_.Kind -eq 'LCurly' }).Count
    $closeBraces = ($tokens | Where-Object { $_.Kind -eq 'RCurly' }).Count
    $openParens = ($tokens | Where-Object { $_.Kind -eq 'LParen' }).Count
    $closeParens = ($tokens | Where-Object { $_.Kind -eq 'RParen' }).Count
    $openBrackets = ($tokens | Where-Object { $_.Kind -eq 'LBracket' }).Count
    $closeBrackets = ($tokens | Where-Object { $_.Kind -eq 'RBracket' }).Count
    
    Write-Host "Bracket matching:" -ForegroundColor White
    Write-Host "  Curly braces: $openBraces open, $closeBraces close" -ForegroundColor $(if ($openBraces -eq $closeBraces) { 'Green' } else { 'Red' })
    Write-Host "  Parentheses: $openParens open, $closeParens close" -ForegroundColor $(if ($openParens -eq $closeParens) { 'Green' } else { 'Red' })
    Write-Host "  Square brackets: $openBrackets open, $closeBrackets close" -ForegroundColor $(if ($openBrackets -eq $closeBrackets) { 'Green' } else { 'Red' })
    
} catch {
    Write-Host "ERROR: Failed to check syntax: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
