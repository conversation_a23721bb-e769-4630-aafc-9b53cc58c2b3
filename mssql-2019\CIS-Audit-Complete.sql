/*
================================================================================
Microsoft SQL Server 2019 CIS Controls v8 Complete Audit Script - Enhanced
================================================================================
Description: Comprehensive audit script for SQL Server 2019 CIS Controls v8 compliance
Version: 1.2 Enhanced (SQL Server 2019 Compatible)
Created: August 17, 2025
Enhanced: August 18, 2025
Updated: September 16, 2025 - Added Database User Assessment (4.5) and Password Change History (4.6)

INSTRUCTIONS FOR ADMIN:
1. Copy this file to the Windows Server hosting SQL Server 2019
2. Run using sqlcmd with output to JSON file:

   sqlcmd -S ServerName\InstanceName -E -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

   OR with SQL Server authentication:
   sqlcmd -S ServerName\InstanceName -U username -P password -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

3. Send the generated CIS-Audit-Results.json file back for analysis

CRITICAL: This script is 100% READ-ONLY and safe for production environments
================================================================================
*/

-- Set output options for clean JSON
SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

-- Initialize audit session
DECLARE @AuditStartTime DATETIME2 = GETDATE();
DECLARE @AuditID UNIQUEIDENTIFIER = NEWID();

-- Environment size detection variables for result limiting
DECLARE @DatabaseCount INT;
DECLARE @LoginCount INT;
DECLARE @IsLargeEnvironment BIT = 0;

-- Detect environment size to apply appropriate result limiting
SELECT @DatabaseCount = COUNT(*) FROM sys.databases WHERE database_id > 4; -- User databases only
SELECT @LoginCount = COUNT(*) FROM sys.sql_logins WHERE is_disabled = 0; -- Active SQL logins only

-- Set large environment flag (100+ databases OR 50+ active SQL logins)
IF @DatabaseCount >= 100 OR @LoginCount >= 50
    SET @IsLargeEnvironment = 1;

-- =============================================================================
-- AUDIT METADATA AND ENVIRONMENT INFORMATION
-- =============================================================================

SELECT 'AuditMetadata' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    @AuditID AS AuditID,
    @AuditStartTime AS AuditStartTime,
    @@SERVERNAME AS ServerName,
    SERVERPROPERTY('ProductVersion') AS ProductVersion,
    SERVERPROPERTY('ProductLevel') AS ProductLevel,
    SERVERPROPERTY('Edition') AS Edition,
    SERVERPROPERTY('EngineEdition') AS EngineEdition,
    SERVERPROPERTY('MachineName') AS MachineName,
    SERVERPROPERTY('InstanceName') AS InstanceName,
    CASE
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) >= 15 THEN 'SQL Server 2019+'
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) = 14 THEN 'SQL Server 2017'
        WHEN CAST(SERVERPROPERTY('ProductMajorVersion') AS INT) = 13 THEN 'SQL Server 2016'
        ELSE 'Older Version'
    END AS VersionCategory,
    'CIS Controls v8.1' AS ComplianceFramework,
    '20 Critical Controls Assessment' AS AssessmentScope,
    USER_NAME() AS AuditUser,
    @DatabaseCount AS UserDatabaseCount,
    @LoginCount AS ActiveSQLLoginCount,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Large Environment - Result Limiting Applied' ELSE 'Standard Environment' END AS EnvironmentSize
FOR JSON PATH, ROOT('AuditMetadata');

-- =============================================================================
-- CIS CONTROL 4.1: AUTHENTICATION MODE ASSESSMENT
-- =============================================================================

SELECT 'CIS_4_1_AuthenticationMode' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    'CIS-4.1' AS ControlID,
    'SQL Server Authentication Mode' AS ControlName,
    'High' AS RiskLevel,
    7.5 AS CVSSScore,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Windows Authentication Only'
        ELSE 'Mixed Mode Authentication'
    END AS CurrentValue,
    'Windows Authentication Only' AS BaselineValue,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 100
        ELSE 0
    END AS ComplianceScore,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'Configuration meets CIS baseline - Windows Authentication provides stronger security'
        ELSE 'Mixed mode authentication enabled - increases attack surface for brute force attacks'
    END AS Finding,
    CASE 
        WHEN SERVERPROPERTY('IsIntegratedSecurityOnly') = 1 THEN 'No action required - Windows Authentication mode is properly configured'
        ELSE 'Change authentication mode to Windows Authentication only using SQL Server Configuration Manager'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate
FOR JSON PATH, ROOT('CIS_4_1_AuthenticationMode');

-- =============================================================================
-- CIS CONTROL 4.2: DEFAULT ACCOUNT SECURITY ASSESSMENT
-- =============================================================================

SELECT 'CIS_4_2_DefaultAccounts' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    'CIS-4.2' AS ControlID,
    'Default Database User Accounts' AS ControlName,
    'Critical' AS RiskLevel,
    9.1 AS CVSSScore,
    name AS AccountName,
    CASE 
        WHEN name = 'sa' AND is_disabled = 1 THEN 'Compliant - SA account disabled'
        WHEN name = 'sa' AND is_disabled = 0 THEN 'Non-Compliant - SA account enabled'
        WHEN name = 'guest' THEN 'Guest account present - requires database-level check'
        ELSE 'Other default account found'
    END AS CurrentStatus,
    CASE 
        WHEN name = 'sa' THEN 'Disabled'
        WHEN name = 'guest' THEN 'No database access'
        ELSE 'Secured/Disabled'
    END AS BaselineValue,
    CASE 
        WHEN name = 'sa' AND is_disabled = 1 THEN 'Compliant'
        WHEN name = 'sa' AND is_disabled = 0 THEN 'Non-Compliant'
        ELSE 'Review Required'
    END AS ComplianceStatus,
    CASE 
        WHEN name = 'sa' AND is_disabled = 1 THEN 100
        WHEN name = 'sa' AND is_disabled = 0 THEN 0
        ELSE 50
    END AS ComplianceScore,
    is_disabled,
    create_date,
    CASE 
        WHEN name = 'sa' AND is_disabled = 0 THEN 'Immediate action required: ALTER LOGIN [sa] DISABLE;'
        WHEN name = 'guest' THEN 'Check database permissions: REVOKE CONNECT FROM [guest];'
        ELSE 'Review account necessity and disable if not required'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate
FROM sys.sql_logins 
WHERE name IN ('sa', 'guest')
FOR JSON PATH, ROOT('CIS_4_2_DefaultAccounts');

-- =============================================================================
-- CIS CONTROL 3.1: TRANSPARENT DATA ENCRYPTION ASSESSMENT
-- =============================================================================

SELECT 'CIS_3_1_TransparentDataEncryption' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT
    'CIS-3.1' AS ControlID,
    'Transparent Data Encryption (TDE)' AS ControlName,
    'High' AS RiskLevel,
    7.2 AS CVSSScore,
    db.name AS DatabaseName,
    ISNULL(dek.encryption_state, 0) AS EncryptionState,
    CASE
        WHEN dek.encryption_state = 0 OR dek.encryption_state IS NULL THEN 'Not Encrypted'
        WHEN dek.encryption_state = 1 THEN 'Encryption in Progress'
        WHEN dek.encryption_state = 2 THEN 'Encryption Key Change in Progress'
        WHEN dek.encryption_state = 3 THEN 'Encrypted'
        WHEN dek.encryption_state = 4 THEN 'Key Change in Progress'
        WHEN dek.encryption_state = 5 THEN 'Decryption in Progress'
        WHEN dek.encryption_state = 6 THEN 'Protection Change in Progress'
        ELSE 'Unknown State'
    END AS EncryptionStatus,
    'Encrypted (State 3)' AS BaselineValue,
    CASE
        WHEN dek.encryption_state = 3 THEN 'Compliant'
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 'In Progress'
        WHEN dek.encryption_state = 5 THEN 'Decrypting - Non-Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE
        WHEN dek.encryption_state = 3 THEN 100
        WHEN dek.encryption_state IN (1, 2, 4, 6) THEN 50
        ELSE 0
    END AS ComplianceScore,
    dek.encryption_algorithm_desc,
    dek.create_date AS EncryptionKeyCreateDate,
    CASE
        WHEN dek.encryption_state = 3 THEN 'Database properly encrypted with TDE'
        WHEN dek.encryption_state IS NULL THEN 'Database not encrypted - implement TDE for data protection'
        ELSE 'Database encryption in transition - monitor progress'
    END AS Finding,
    CASE
        WHEN dek.encryption_state IS NULL THEN 'Implement TDE: CREATE DATABASE ENCRYPTION KEY WITH ALGORITHM = AES_256; ALTER DATABASE [' + db.name + '] SET ENCRYPTION ON;'
        WHEN dek.encryption_state = 3 THEN 'No action required - TDE properly implemented'
        ELSE 'Monitor encryption progress and ensure completion'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Results limited to first 50 databases for large environment' ELSE 'Complete assessment' END AS ResultScope
FROM (
    SELECT TOP (CASE WHEN @IsLargeEnvironment = 1 THEN 50 ELSE 1000 END) *
    FROM sys.databases
    WHERE database_id > 4 -- Exclude system databases
    ORDER BY name
) db
LEFT JOIN sys.dm_database_encryption_keys dek ON db.database_id = dek.database_id
FOR JSON PATH, ROOT('CIS_3_1_TransparentDataEncryption');

-- =============================================================================
-- CIS CONTROL 8.1: SERVER AUDIT CONFIGURATION ASSESSMENT
-- =============================================================================

SELECT 'CIS_8_1_ServerAudit' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    'CIS-8.1' AS ControlID,
    'SQL Server Audit Configuration' AS ControlName,
    'High' AS RiskLevel,
    6.7 AS CVSSScore,
    ISNULL(COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END), 0) AS EnabledAudits,
    ISNULL(COUNT(*), 0) AS TotalAudits,
    CASE 
        WHEN COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END) > 0 THEN 'At least one audit enabled'
        ELSE 'No audits enabled'
    END AS CurrentValue,
    'At least one audit enabled' AS BaselineValue,
    CASE 
        WHEN COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END) > 0 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END) > 0 THEN 100
        ELSE 0
    END AS ComplianceScore,
    CASE 
        WHEN COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END) > 0 THEN 'Server audit properly configured for security monitoring'
        ELSE 'No server audits enabled - security events not being captured'
    END AS Finding,
    CASE
        WHEN COUNT(CASE WHEN is_state_enabled = 1 THEN 1 END) = 0 THEN 'Create server audit: CREATE SERVER AUDIT [SecurityAudit] TO FILE (FILEPATH=''C:\SQLAudit\''); ALTER SERVER AUDIT [SecurityAudit] WITH (STATE=ON);'
        ELSE 'Server audit properly configured - ensure audit specifications are defined'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate
FROM sys.server_audits
FOR JSON PATH, ROOT('CIS_8_1_ServerAudit');

-- =============================================================================
-- CIS CONTROL 5.2: CONFIGURATION SECURITY ASSESSMENT
-- =============================================================================

SELECT 'CIS_5_2_ConfigurationSecurity' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    'CIS-5.2' AS ControlID,
    'SQL Server Configuration Security' AS ControlName,
    'High' AS RiskLevel,
    8.2 AS CVSSScore,
    name AS ConfigurationOption,
    value_in_use AS CurrentValue,
    0 AS BaselineValue,
    CASE 
        WHEN value_in_use = 0 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN value_in_use = 0 THEN 100
        ELSE 0
    END AS ComplianceScore,
    CASE 
        WHEN value_in_use = 0 THEN 'Dangerous feature properly disabled'
        ELSE 'Dangerous feature enabled - security risk'
    END AS Finding,
    CASE 
        WHEN value_in_use = 0 THEN 'No action required - feature properly disabled'
        ELSE 'Disable dangerous feature: EXEC sp_configure ''' + name + ''', 0; RECONFIGURE;'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate
FROM sys.configurations 
WHERE name IN ('xp_cmdshell', 'Ole Automation Procedures', 'SQL Mail XPs', 'Database Mail XPs', 'Ad Hoc Distributed Queries')
FOR JSON PATH, ROOT('CIS_5_2_ConfigurationSecurity');

-- =============================================================================
-- CIS CONTROL 12.3: REMOTE ACCESS SECURITY ASSESSMENT
-- =============================================================================

SELECT 'CIS_12_3_RemoteAccess' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT 
    'CIS-12.3' AS ControlID,
    'Remote Access Security' AS ControlName,
    'Medium' AS RiskLevel,
    5.0 AS CVSSScore,
    name AS ConfigurationOption,
    value_in_use AS CurrentValue,
    CASE 
        WHEN name = 'remote access' THEN 0
        WHEN name = 'remote admin connections' THEN 1
        ELSE 0
    END AS BaselineValue,
    CASE 
        WHEN name = 'remote access' AND value_in_use = 0 THEN 'Compliant'
        WHEN name = 'remote admin connections' AND value_in_use = 1 THEN 'Compliant'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE 
        WHEN (name = 'remote access' AND value_in_use = 0) OR 
             (name = 'remote admin connections' AND value_in_use = 1) THEN 100
        ELSE 0
    END AS ComplianceScore,
    CASE 
        WHEN name = 'remote access' AND value_in_use = 0 THEN 'Remote access properly disabled'
        WHEN name = 'remote admin connections' AND value_in_use = 1 THEN 'Dedicated Admin Connection (DAC) properly enabled'
        WHEN name = 'remote access' AND value_in_use = 1 THEN 'Remote access enabled - review necessity'
        WHEN name = 'remote admin connections' AND value_in_use = 0 THEN 'DAC disabled - should be enabled for emergency access'
        ELSE 'Configuration requires review'
    END AS Finding,
    CASE 
        WHEN name = 'remote access' AND value_in_use = 1 THEN 'Consider disabling: EXEC sp_configure ''remote access'', 0; RECONFIGURE;'
        WHEN name = 'remote admin connections' AND value_in_use = 0 THEN 'Enable DAC: EXEC sp_configure ''remote admin connections'', 1; RECONFIGURE;'
        ELSE 'Configuration is appropriate'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate
FROM sys.configurations 
WHERE name IN ('remote access', 'remote admin connections')
FOR JSON PATH, ROOT('CIS_12_3_RemoteAccess');

-- =============================================================================
-- CIS CONTROL 4.4: PASSWORD POLICY ENFORCEMENT (SQL Server 2019 Windows)
-- =============================================================================

SELECT 'CIS_4_4_PasswordPolicy' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT
    'CIS-4.4' AS ControlID,
    'Password Policy Enforcement' AS ControlName,
    'Medium' AS RiskLevel,
    5.3 AS CVSSScore,
    name AS LoginName,
    is_policy_checked,
    is_expiration_checked,
    CASE
        WHEN is_policy_checked = 1 AND is_expiration_checked = 1 THEN 'Compliant'
        WHEN is_policy_checked = 0 OR is_expiration_checked = 0 THEN 'Non-Compliant'
        ELSE 'Unknown'
    END AS ComplianceStatus,
    CASE
        WHEN is_policy_checked = 1 AND is_expiration_checked = 1 THEN 100
        WHEN is_policy_checked = 1 OR is_expiration_checked = 1 THEN 50
        ELSE 0
    END AS ComplianceScore,
    'Policy and expiration enforcement enabled' AS BaselineValue,
    CASE
        WHEN is_policy_checked = 1 AND is_expiration_checked = 1 THEN 'Password policy properly enforced'
        WHEN is_policy_checked = 0 THEN 'Password policy checking disabled - security risk'
        WHEN is_expiration_checked = 0 THEN 'Password expiration checking disabled - security risk'
        ELSE 'Password policy configuration requires review'
    END AS Finding,
    CASE
        WHEN is_policy_checked = 0 THEN 'Enable password policy: ALTER LOGIN [' + name + '] WITH CHECK_POLICY = ON;'
        WHEN is_expiration_checked = 0 THEN 'Enable password expiration: ALTER LOGIN [' + name + '] WITH CHECK_EXPIRATION = ON;'
        ELSE 'Password policy properly configured'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Results limited to first 25 SQL logins for large environment' ELSE 'Complete assessment' END AS ResultScope
FROM (
    SELECT TOP (CASE WHEN @IsLargeEnvironment = 1 THEN 25 ELSE 100 END) *
    FROM sys.sql_logins
    WHERE type = 'S' AND is_disabled = 0 -- Active SQL logins only
    ORDER BY name
) AS limited_logins
FOR JSON PATH, ROOT('CIS_4_4_PasswordPolicy');

-- =============================================================================
-- CIS CONTROL 3.2: CONNECTION ENCRYPTION (Windows Registry Check)
-- =============================================================================

SELECT 'CIS_3_2_ConnectionEncryption' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

-- Note: This requires xp_regread which may need to be enabled
-- Registry path for SQL Server 2019: HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQLServer\SuperSocketNetLib

DECLARE @ForceEncryption INT;
DECLARE @RegistryPath NVARCHAR(500) = 'SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.' + ISNULL(CAST(SERVERPROPERTY('InstanceName') AS NVARCHAR(128)), 'MSSQLSERVER') + '\MSSQLServer\SuperSocketNetLib';

-- Attempt to read ForceEncryption registry value
BEGIN TRY
    EXEC master.dbo.xp_regread
        @rootkey = 'HKEY_LOCAL_MACHINE',
        @key = @RegistryPath,
        @value_name = 'ForceEncryption',
        @value = @ForceEncryption OUTPUT;
END TRY
BEGIN CATCH
    SET @ForceEncryption = -1; -- Indicates registry read failed
END CATCH

SELECT
    'CIS-3.2' AS ControlID,
    'Connection Encryption Configuration' AS ControlName,
    'High' AS RiskLevel,
    6.9 AS CVSSScore,
    CASE
        WHEN @ForceEncryption = 1 THEN 'Force Encryption Enabled'
        WHEN @ForceEncryption = 0 THEN 'Force Encryption Disabled'
        WHEN @ForceEncryption = -1 THEN 'Registry Check Failed - Manual Verification Required'
        ELSE 'Unknown Configuration'
    END AS CurrentValue,
    'Force Encryption Enabled' AS BaselineValue,
    CASE
        WHEN @ForceEncryption = 1 THEN 'Compliant'
        WHEN @ForceEncryption = 0 THEN 'Non-Compliant'
        ELSE 'Manual Verification Required'
    END AS ComplianceStatus,
    CASE
        WHEN @ForceEncryption = 1 THEN 100
        WHEN @ForceEncryption = 0 THEN 0
        ELSE 50
    END AS ComplianceScore,
    CASE
        WHEN @ForceEncryption = 1 THEN 'All client connections properly encrypted'
        WHEN @ForceEncryption = 0 THEN 'Client connections not forced to encrypt - data transmission risk'
        ELSE 'Unable to verify encryption configuration - manual check required'
    END AS Finding,
    CASE
        WHEN @ForceEncryption = 0 THEN 'Enable Force Encryption using SQL Server Configuration Manager'
        WHEN @ForceEncryption = -1 THEN 'Manually verify Force Encryption setting in SQL Server Configuration Manager'
        ELSE 'Connection encryption properly configured'
    END AS Recommendation,
    @RegistryPath AS RegistryPath,
    @AuditStartTime AS AssessmentDate
FOR JSON PATH, ROOT('CIS_3_2_ConnectionEncryption');

-- =============================================================================
-- CIS CONTROL 4.5: DATABASE USER ASSESSMENT
-- =============================================================================

SELECT 'CIS_4_5_DatabaseUsers' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

WITH DatabaseUsers AS (
    SELECT
        d.name AS DatabaseName,
        dp.name AS UserName,
        dp.type_desc AS UserType,
        CASE
            WHEN dp.is_disabled = 1 THEN 'No'
            ELSE 'Yes'
        END AS IsEnabled,
        CASE
            WHEN sl.is_policy_checked = 1 THEN 'Yes'
            WHEN sl.is_policy_checked = 0 THEN 'No'
            ELSE 'N/A (Windows User)'
        END AS PasswordPolicyApplied,
        CASE
            WHEN sl.is_expiration_checked = 1 THEN 'Yes'
            WHEN sl.is_expiration_checked = 0 THEN 'No'
            ELSE 'N/A (Windows User)'
        END AS PasswordExpires,
        ISNULL(CONVERT(VARCHAR(19), sl.modify_date, 120), 'N/A') AS LastPasswordChanged,
        dp.create_date AS UserCreateDate,
        ROW_NUMBER() OVER (ORDER BY d.name, dp.name) AS RowNum
    FROM sys.databases d
    CROSS APPLY (
        SELECT TOP (CASE WHEN @IsLargeEnvironment = 1 THEN 10 ELSE 50 END) *
        FROM sys.database_principals dp
        WHERE dp.type IN ('S', 'U', 'G') -- SQL users, Windows users, Windows groups
        AND dp.principal_id > 4 -- Exclude system principals
        AND dp.name NOT IN ('guest', 'INFORMATION_SCHEMA', 'sys')
        ORDER BY dp.name
    ) dp
    LEFT JOIN sys.sql_logins sl ON dp.sid = sl.sid
    WHERE d.database_id > 4 -- Exclude system databases
    AND d.state = 0 -- Online databases only
)
SELECT
    'CIS-4.5' AS ControlID,
    'Database User Security Assessment' AS ControlName,
    'Medium' AS RiskLevel,
    5.8 AS CVSSScore,
    DatabaseName,
    UserName,
    UserType,
    IsEnabled,
    PasswordPolicyApplied,
    PasswordExpires,
    LastPasswordChanged,
    UserCreateDate,
    CASE
        WHEN IsEnabled = 'Yes' AND PasswordPolicyApplied = 'Yes' AND PasswordExpires = 'Yes' THEN 'Compliant'
        WHEN IsEnabled = 'Yes' AND (PasswordPolicyApplied = 'No' OR PasswordExpires = 'No') THEN 'Partial Compliance'
        WHEN IsEnabled = 'No' THEN 'Disabled User'
        ELSE 'Review Required'
    END AS ComplianceStatus,
    CASE
        WHEN IsEnabled = 'Yes' AND PasswordPolicyApplied = 'Yes' AND PasswordExpires = 'Yes' THEN 100
        WHEN IsEnabled = 'Yes' AND (PasswordPolicyApplied = 'Yes' OR PasswordExpires = 'Yes') THEN 75
        WHEN IsEnabled = 'No' THEN 50
        ELSE 25
    END AS ComplianceScore,
    'Enabled with password policy and expiration' AS BaselineValue,
    CASE
        WHEN IsEnabled = 'Yes' AND PasswordPolicyApplied = 'Yes' AND PasswordExpires = 'Yes' THEN 'Database user properly secured with password controls'
        WHEN IsEnabled = 'Yes' AND PasswordPolicyApplied = 'No' THEN 'Database user lacks password policy enforcement'
        WHEN IsEnabled = 'Yes' AND PasswordExpires = 'No' THEN 'Database user password does not expire'
        WHEN IsEnabled = 'No' THEN 'Database user account is disabled'
        ELSE 'Database user configuration requires review'
    END AS Finding,
    CASE
        WHEN IsEnabled = 'Yes' AND PasswordPolicyApplied = 'No' THEN 'Enable password policy for SQL login associated with this user'
        WHEN IsEnabled = 'Yes' AND PasswordExpires = 'No' THEN 'Enable password expiration for SQL login associated with this user'
        WHEN IsEnabled = 'No' THEN 'Consider removing disabled user account if no longer needed'
        ELSE 'Database user configuration is appropriate'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Results limited to first 10 users per database for large environment' ELSE 'Complete user assessment' END AS ResultScope
FROM DatabaseUsers
WHERE RowNum <= (CASE WHEN @IsLargeEnvironment = 1 THEN 200 ELSE 1000 END) -- Overall result limiting
FOR JSON PATH, ROOT('CIS_4_5_DatabaseUsers');

-- =============================================================================
-- CIS CONTROL 4.6: PASSWORD CHANGE HISTORY ASSESSMENT
-- =============================================================================

SELECT 'CIS_4_6_PasswordHistory' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

WITH PasswordHistory AS (
    SELECT
        sl.name AS LoginName,
        sl.create_date AS LoginCreateDate,
        sl.modify_date AS LastPasswordChange,
        CASE
            WHEN sl.modify_date > DATEADD(DAY, -90, GETDATE()) THEN 'Recent (< 90 days)'
            WHEN sl.modify_date > DATEADD(DAY, -180, GETDATE()) THEN 'Moderate (90-180 days)'
            WHEN sl.modify_date > DATEADD(DAY, -365, GETDATE()) THEN 'Old (180-365 days)'
            ELSE 'Very Old (> 365 days)'
        END AS PasswordAge,
        DATEDIFF(DAY, sl.modify_date, GETDATE()) AS DaysSinceLastChange,
        sl.is_policy_checked,
        sl.is_expiration_checked,
        sl.is_disabled,
        -- Attempt to get audit information if available
        CASE
            WHEN EXISTS (SELECT 1 FROM sys.server_audits WHERE is_state_enabled = 1) THEN 'Audit Enabled - Check audit logs'
            ELSE 'No Active Audit - Limited History Available'
        END AS AuditTrailStatus,
        ROW_NUMBER() OVER (ORDER BY sl.modify_date DESC, sl.name) AS RowNum
    FROM sys.sql_logins sl
    WHERE sl.type = 'S' -- SQL Server logins only
    AND sl.name <> 'sa' -- Exclude SA account (covered in other control)
)
SELECT
    'CIS-4.6' AS ControlID,
    'Password Change History Assessment' AS ControlName,
    'Medium' AS RiskLevel,
    6.1 AS CVSSScore,
    LoginName,
    LoginCreateDate,
    LastPasswordChange,
    PasswordAge,
    DaysSinceLastChange,
    CASE
        WHEN DaysSinceLastChange <= 90 THEN 'Compliant'
        WHEN DaysSinceLastChange <= 180 THEN 'Attention Required'
        ELSE 'Non-Compliant'
    END AS ComplianceStatus,
    CASE
        WHEN DaysSinceLastChange <= 90 THEN 100
        WHEN DaysSinceLastChange <= 180 THEN 60
        WHEN DaysSinceLastChange <= 365 THEN 30
        ELSE 0
    END AS ComplianceScore,
    'Password changed within 90 days' AS BaselineValue,
    is_policy_checked AS PolicyChecked,
    is_expiration_checked AS ExpirationChecked,
    is_disabled AS IsDisabled,
    AuditTrailStatus,
    CASE
        WHEN DaysSinceLastChange <= 90 THEN 'Password recently changed - good security practice'
        WHEN DaysSinceLastChange <= 180 THEN 'Password moderately old - consider change policy review'
        WHEN DaysSinceLastChange <= 365 THEN 'Password old - requires attention'
        ELSE 'Password very old - immediate attention required'
    END AS Finding,
    CASE
        WHEN DaysSinceLastChange > 180 AND is_expiration_checked = 0 THEN 'Enable password expiration: ALTER LOGIN [' + LoginName + '] WITH CHECK_EXPIRATION = ON;'
        WHEN DaysSinceLastChange > 180 AND is_policy_checked = 0 THEN 'Enable password policy: ALTER LOGIN [' + LoginName + '] WITH CHECK_POLICY = ON;'
        WHEN DaysSinceLastChange > 365 THEN 'Force password change: ALTER LOGIN [' + LoginName + '] WITH PASSWORD = ''NewSecurePassword123!'' MUST_CHANGE;'
        WHEN DaysSinceLastChange > 180 THEN 'Review password change policy and consider forcing password update'
        ELSE 'Password change history is within acceptable limits'
    END AS Recommendation,
    @AuditStartTime AS AssessmentDate,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Results limited to first 30 SQL logins for large environment' ELSE 'Complete password history assessment' END AS ResultScope
FROM PasswordHistory
WHERE RowNum <= (CASE WHEN @IsLargeEnvironment = 1 THEN 30 ELSE 100 END)
FOR JSON PATH, ROOT('CIS_4_6_PasswordHistory');

-- =============================================================================
-- COMPLIANCE SUMMARY
-- =============================================================================

SELECT 'ComplianceSummary' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT
    'CIS Controls v8 Compliance Summary' AS AssessmentType,
    11 AS TotalControlsAssessed,
    @AuditStartTime AS AuditStartTime,
    GETDATE() AS AuditEndTime,
    DATEDIFF(SECOND, @AuditStartTime, GETDATE()) AS AuditDurationSeconds,
    @AuditID AS AuditID,
    @@SERVERNAME AS ServerName,
    'SQL Server 2019 Windows Compatible' AS FrameworkVersion,
    'Read-Only Assessment - Production Safe' AS AuditType,
    USER_NAME() AS AuditExecutedBy,
    @DatabaseCount AS TotalUserDatabases,
    @LoginCount AS TotalActiveSQLLogins,
    CASE WHEN @IsLargeEnvironment = 1 THEN 'Large Environment - Results Limited' ELSE 'Standard Environment - Complete Results' END AS EnvironmentType,
    CASE
        WHEN @IsLargeEnvironment = 1 THEN 'TDE: Top 50 databases, Password Policy: Top 25 logins, DB Users: Top 10 per database, Password History: Top 30 logins'
        ELSE 'All databases, logins, and users assessed'
    END AS ResultLimitations
FOR JSON PATH, ROOT('ComplianceSummary');

-- =============================================================================
-- OUTPUT SIZE MONITORING AND ESTIMATION
-- =============================================================================

SELECT 'OutputSizeEstimation' AS AssessmentCategory FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;

SELECT
    'Output Buffer Monitoring' AS MonitoringType,
    @DatabaseCount AS DatabasesAssessed,
    @LoginCount AS LoginsAssessed,
    CASE
        WHEN @IsLargeEnvironment = 1 THEN 'Large Environment - Result Limiting Applied'
        ELSE 'Standard Environment - Complete Assessment'
    END AS EnvironmentClassification,
    CASE
        WHEN @DatabaseCount + @LoginCount < 50 THEN 'Small (< 8KB estimated)'
        WHEN @DatabaseCount + @LoginCount < 150 THEN 'Medium (8-15KB estimated)'
        WHEN @DatabaseCount + @LoginCount < 300 THEN 'Large (15-30KB estimated)'
        ELSE 'Very Large (30KB+ estimated)'
    END AS EstimatedOutputSize,
    CASE
        WHEN @DatabaseCount + @LoginCount > 500 THEN 'Consider running assessment in smaller batches'
        WHEN @DatabaseCount + @LoginCount > 200 THEN 'Monitor sqlcmd output buffer capacity'
        ELSE 'Output size within normal limits'
    END AS BufferRecommendation,
    GETDATE() AS EstimationTimestamp
FOR JSON PATH, ROOT('OutputSizeEstimation');

-- =============================================================================
-- END OF AUDIT SCRIPT
-- =============================================================================

PRINT '';
PRINT '================================================================================';
PRINT 'CIS Controls v8 Audit Completed Successfully - Enhanced Version v1.2';
PRINT 'Features: Environment size detection, result limiting, buffer monitoring';
PRINT 'New Controls: Database User Assessment (4.5), Password Change History (4.6)';
PRINT 'Total Controls Assessed: 11 (was 9)';
PRINT 'Output saved to: CIS-Audit-Results.json';
PRINT 'Send this file for compliance analysis and reporting';
PRINT '================================================================================';

/*
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN (Windows Server):
================================================================================

1. WINDOWS SERVER LOCAL EXECUTION (Recommended):
   sqlcmd -S .\MSSQLSERVER -E -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

2. WINDOWS SERVER WITH INSTANCE NAME:
   sqlcmd -S ServerName\InstanceName -E -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

3. SQL SERVER AUTHENTICATION:
   sqlcmd -S ServerName\InstanceName -U username -P password -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

4. REMOTE EXECUTION FROM ANOTHER WINDOWS MACHINE:
   sqlcmd -S "ServerName,1433" -E -i CIS-Audit-Complete.sql -o CIS-Audit-Results.json

5. USING SQL SERVER MANAGEMENT STUDIO:
   - Open CIS-Audit-Complete.sql in SSMS
   - Execute the script
   - Copy results from Messages tab to CIS-Audit-Results.json file

PREREQUISITES:
- SQL Server 2019 on Windows Server
- SQL Server Command Line Utilities (sqlcmd)
- User account with VIEW SERVER STATE and VIEW ANY DEFINITION permissions
- xp_regread enabled for registry checks (optional - script handles if disabled)

IMPORTANT NOTES:
- This script is 100% READ-ONLY and safe for production
- No data modifications are performed
- Output is in JSON format for easy processing
- Script assesses 11 critical CIS Controls v8 configurations
- Optimized for SQL Server 2019 on Windows Server
- Includes Windows registry-based configuration checks

ENHANCED FEATURES (v1.1):
- Environment size detection (100+ databases or 50+ SQL logins)
- Automatic result limiting for large environments to prevent buffer overflow
- Output size estimation and buffer monitoring
- Enhanced registry path buffer (500 characters vs 255)
- Optimized recommendation text for better display formatting
- Large environment handling: TDE limited to 50 databases, Password Policy to 25 logins

NEW FEATURES (v1.2):
- CIS Control 4.5: Database User Assessment across all user databases
- CIS Control 4.6: Password Change History Assessment for SQL logins
- Enhanced user security analysis with password policy compliance
- Password aging analysis with risk-based recommendations
- Cross-database user enumeration with security status
- Large environment handling: DB Users limited to 10 per database, Password History to 30 logins

SEND BACK: The generated CIS-Audit-Results.json file
================================================================================
*/
