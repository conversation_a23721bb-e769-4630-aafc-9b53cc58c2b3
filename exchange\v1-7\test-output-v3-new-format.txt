﻿================================================================================
EXCHANGE MAILBOX SECURITY AUDIT REPORT
================================================================================

Report Generated: 2025-09-19 16:43:52
Source JSON File: Sample-Exchange-Mailbox-Security-Results-50-Realistic.json
Conversion Script: Exchange JSON to TXT Converter v3.0.0
Created By: E.Z. Consultancy

================================================================================
AUDIT METADATA
================================================================================

Audit ID: b8f3e2d1-4c5a-6789-bcde-f012345678ab
Organization: TechCorp International
Exchange Version: 
Audit Start Time: 2025-09-14 10:15:30
Audit End Time: 
Audit Duration: 
Script Version: 1.6.7
Script Release Date: September 11, 2025
Script Author: E.Z. Consultancy
PowerShell Version: 5.1.19041.4648
PowerShell Edition: Desktop
Executed By: 
Execution Host: EXCH-MGMT-02
Total Mailboxes: 847
Max Mailbox Sample: 500
Audit Scope: 5 Critical Mailbox Security Controls
Audit User: svc-audit
Domain Filter Enabled: True
Filtered Domains: techcorp.com, subsidiary.techcorp.com
Domain Filter Scope: Domain-specific audit for: techcorp.com, subsidiary.techcorp.com
Exchange Servers: 4

================================================================================
EXECUTIVE SUMMARY
================================================================================

Overall Compliance Score: 83.2%
Assessment Success Rate: 100%

CONTROL ASSESSMENT SUMMARY:
  Total Controls Assessed: 5
  Controls Compliant: 2
  Controls Requiring Review: 3
  Controls Non-Compliant: 0
  Controls with No Data: 0

RISK ASSESSMENT SUMMARY:
  Critical Findings: 1
  High Risk Findings: 2
  Medium Risk Findings: 2
  Low Risk Findings: 0

AUDIT EXECUTION SUMMARY:
  Successful Assessments: 5
  Failed Assessments: 0
  Audit Duration: 1035.67 seconds
  Assessment Type: Exchange Server Mailbox Security Controls Audit

================================================================================
MAILBOX IMPERSONATION RIGHTS (MBX-1.1)
================================================================================

Control ID: MBX-1.1
Risk Level: Critical
CVSS Score: 9.3
Compliance Status: Review Required
Compliance Score: 75%
Sample Size: 

FINDING:
  Limited impersonation rights - review assignments

RECOMMENDATION:
  Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions

IMPERSONATION RIGHTS COMPREHENSIVE DETAILS:
  Total Impersonation Assignments: 4

  COMPLETE IMPERSONATION RIGHTS LISTING:
    [1] Impersonation Assignment:
        Role Assignee: Exchange Backup Service
        Full DN: TECHCORP\svc-exchange-backup
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-01-15 09:30:00
        Is Valid: True

    [2] Impersonation Assignment:
        Role Assignee: Compliance Service Account
        Full DN: TECHCORP\svc-compliance
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-02-20 14:15:00
        Is Valid: True

    [3] Impersonation Assignment:
        Role Assignee: Exchange Administrator
        Full DN: TECHCORP\admin-exchange
        Assignee Type: User
        Assignment Method: RoleGroup
        When Created: 2025-01-10 11:45:00
        Is Valid: True

    [4] Impersonation Assignment:
        Role Assignee: Exchange Monitoring Service
        Full DN: TECHCORP\svc-monitoring
        Assignee Type: User
        Assignment Method: Direct
        When Created: 2025-03-05 16:20:00
        Is Valid: True


================================================================================
MAILBOX FULL ACCESS PERMISSIONS (MBX-2.1)
================================================================================

Control ID: MBX-2.1
Risk Level: High
CVSS Score: 8.1
Compliance Status: Review Required
Compliance Score: 72%
Sample Size: 50

FINDING:
  High number of Full Access permissions - review required

RECOMMENDATION:
  Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.

FULL ACCESS PERMISSIONS COMPREHENSIVE DETAILS:
  Total Full Access Permissions: 89
  Unique Users with Full Access: 67

  COMPLETE FULL ACCESS PERMISSIONS LISTING:
    [1] Full Access Entry:
        Mailbox: John Doe
        Email: <EMAIL>
        User Account: TECHCORP\jane.smith
        Access Rights: FullAccess
        Inherited: False

    Summary: jane.smith has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [2] Full Access Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Access Rights: FullAccess
        Inherited: False

    Summary: executive.assistant has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [3] Full Access Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        User Account: TECHCORP\robert.wilson
        Access Rights: FullAccess
        Inherited: False

    Summary: robert.wilson has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [4] Full Access Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        User Account: TECHCORP\sarah.johnson
        Access Rights: FullAccess
        Inherited: False

    Summary: sarah.johnson has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [5] Full Access Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        User Account: TECHCORP\mike.anderson
        Access Rights: FullAccess
        Inherited: False

    Summary: mike.anderson has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [6] Full Access Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        User Account: TECHCORP\lisa.brown
        Access Rights: FullAccess
        Inherited: False

    Summary: lisa.brown has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [7] Full Access Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        User Account: TECHCORP\david.martinez
        Access Rights: FullAccess
        Inherited: False

    Summary: david.martinez has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [8] Full Access Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        User Account: TECHCORP\amanda.taylor
        Access Rights: FullAccess
        Inherited: False

    Summary: amanda.taylor has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [9] Full Access Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        User Account: TECHCORP\thomas.white
        Access Rights: FullAccess
        Inherited: False

    Summary: thomas.white has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [10] Full Access Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        User Account: TECHCORP\patricia.clark
        Access Rights: FullAccess
        Inherited: False

    Summary: patricia.clark has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [11] Full Access Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        User Account: TECHCORP\nancy.rodriguez
        Access Rights: FullAccess
        Inherited: False

    Summary: nancy.rodriguez has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [12] Full Access Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        User Account: TECHCORP\kevin.lewis
        Access Rights: FullAccess
        Inherited: False

    Summary: kevin.lewis has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [13] Full Access Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        User Account: TECHCORP\stephanie.walker
        Access Rights: FullAccess
        Inherited: False

    Summary: stephanie.walker has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [14] Full Access Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        User Account: TECHCORP\brian.hall
        Access Rights: FullAccess
        Inherited: False

    Summary: brian.hall has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [15] Full Access Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        User Account: TECHCORP\michelle.young
        Access Rights: FullAccess
        Inherited: False

    Summary: michelle.young has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [16] Full Access Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        User Account: TECHCORP\daniel.king
        Access Rights: FullAccess
        Inherited: False

    Summary: daniel.king has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [17] Full Access Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        User Account: TECHCORP\carol.wright
        Access Rights: FullAccess
        Inherited: False

    Summary: carol.wright has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [18] Full Access Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False

    Summary: facilities.admin has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [19] Full Access Entry:
        Mailbox: Conference Room A
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False

    Summary: facilities.admin has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [20] Full Access Entry:
        Mailbox: Conference Room B
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Access Rights: FullAccess
        Inherited: False

    Summary: facilities.admin has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [21] Full Access Entry:
        Mailbox: Executive Board Room
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Access Rights: FullAccess
        Inherited: False

    Summary: executive.assistant has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [22] Full Access Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        User Account: SUBSIDIARY\external.user
        Access Rights: FullAccess
        Inherited: False

    Summary: external.user has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [23] Full Access Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        User Account: TECHCORP\procurement.manager
        Access Rights: FullAccess
        Inherited: False

    Summary: procurement.manager has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [24] Full Access Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        User Account: TECHCORP\svc-compliance
        Access Rights: FullAccess
        Inherited: False

    Summary: svc-compliance has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [25] Full Access Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        User Account: TECHCORP\audit.manager
        Access Rights: FullAccess
        Inherited: False

    Summary: audit.manager has <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown


CROSS-DOMAIN ANALYSIS:

  CROSS-DOMAIN RELATIONSHIPS:
    [1] Cross-Domain Permission:
        Mailbox: Shared Project Alpha
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\external.user
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 5
        Risk Level: High
        Permission Type: Direct
        Inherited: False

    [2] Cross-Domain Permission:
        Mailbox: Vendor Communications
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\vendor.admin
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False

    [3] Cross-Domain Permission:
        Mailbox: Finance Team
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\finance.auditor
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Exchange-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 6
        Risk Level: High
        Permission Type: Direct
        Inherited: False

    [4] Cross-Domain Permission:
        Mailbox: Compliance Officer
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\compliance.manager
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Read-Only-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False

    [5] Cross-Domain Permission:
        Mailbox: Chief Executive Officer
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\board.secretary
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-As
        Is Cross-Domain: True
        Risk Score: 7
        Risk Level: High
        Permission Type: Direct
        Inherited: False

    [6] Cross-Domain Permission:
        Mailbox: Legal Department
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\legal.counsel
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-On-Behalf
        Is Cross-Domain: True
        Risk Score: 3
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False

    [7] Cross-Domain Permission:
        Mailbox: Internal Audit
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\external.auditor
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Exchange-Administrator
        Access Rights: FullAccess
        Is Cross-Domain: True
        Risk Score: 6
        Risk Level: High
        Permission Type: Direct
        Inherited: False

    [8] Cross-Domain Permission:
        Mailbox: HR Shared Mailbox
        Mailbox Email: <EMAIL>
        Mailbox Domain: techcorp.com
        Admin User: SUBSIDIARY\hr.consultant
        Admin Domain: subsidiary.techcorp.com
        Admin Classification: Business-User-Delegate
        Access Rights: Send-As
        Is Cross-Domain: True
        Risk Score: 4
        Risk Level: Medium
        Permission Type: Direct
        Inherited: False

  PERMISSION SUMMARY:
    Domain Relationship: subsidiary.techcorp.com -> techcorp.com
      Total Count: 8
      Admin Domain: subsidiary.techcorp.com
      Mailbox Domain: techcorp.com
      High Risk Count: 4
      Admin Types:
        Business-User-Delegate: 5
        Exchange-Administrator: 2
        Read-Only-Administrator: 1

  RISK ASSESSMENT:
    Total Permission Relationships: 89
    Cross-Domain Relationships: 8
    High Risk Relationships: 4
    Cross-Domain Percentage: 8.99%
    Risk Distribution:
      High: 4
      Medium: 4
      Low: 81


================================================================================
MAILBOX AUDIT LOGGING CONFIGURATION (MBX-3.1)
================================================================================

Control ID: MBX-3.1
Risk Level: Medium
CVSS Score: 6.8
Compliance Status: Review Required
Compliance Score: 96%
Sample Size: 50

FINDING:
  Admin audit logging enabled

RECOMMENDATION:
  Ensure mailbox audit is enabled for all mailboxes

AUDIT LOGGING COMPREHENSIVE DETAILS:
  Admin Audit Logging Enabled: True
  Mailboxes with Audit Enabled: 48
  Audit Bypass Users Count: 2
  Sample Size: 50

  MAILBOX AUDIT BYPASS USERS:
    [1] User: svc-exchange-backup (Bypass: Enabled)
    [2] User: svc-monitoring (Bypass: Enabled)

  SAMPLE MAILBOX AUDIT SETTINGS:
    Total Sample Mailboxes: 49

    [1] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: John Doe
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [2] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Executive Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [3] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Finance Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [4] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Financial Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [5] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Chief Technology Officer
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [6] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: HR Shared Mailbox
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [7] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: IT Support
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [8] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Marketing Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [9] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Sales Team
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    [10] Mailbox Audit Configuration:
        Identity: <EMAIL>
        Display Name: Legal Department
        Audit Enabled: True
        Audit Log Age Limit: 90.00:00:00

    ... and 39 more mailbox audit configurations

  CURRENT ASSESSMENT:
    Admin audit: True, Mailbox audit enabled: 48 of 50 sampled


================================================================================
SEND-AS PERMISSIONS (MBX-4.1)
================================================================================

Control ID: MBX-4.1
Risk Level: High
CVSS Score: 7.6
Compliance Status: Compliant
Compliance Score: 85%
Sample Size: 50

FINDING:
  Send-As permissions appear reasonable

RECOMMENDATION:
  Regularly review Send-As permissions and ensure they are justified by business requirements

SEND-AS PERMISSIONS COMPREHENSIVE DETAILS:
  Total Send-As Permissions: 23
  Unique Users with Send-As: 18

  COMPLETE SEND-AS PERMISSIONS LISTING:
    [1] Send-As Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        User Account: TECHCORP\executive.assistant
        Inherited: False

    Summary: executive.assistant can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [2] Send-As Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        User Account: TECHCORP\robert.wilson
        Inherited: False

    Summary: robert.wilson can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [3] Send-As Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        User Account: TECHCORP\lisa.brown
        Inherited: False

    Summary: lisa.brown can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [4] Send-As Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        User Account: TECHCORP\david.martinez
        Inherited: False

    Summary: david.martinez can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [5] Send-As Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        User Account: TECHCORP\sarah.johnson
        Inherited: False

    Summary: sarah.johnson can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [6] Send-As Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        User Account: TECHCORP\amanda.taylor
        Inherited: False

    Summary: amanda.taylor can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [7] Send-As Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        User Account: TECHCORP\mike.anderson
        Inherited: False

    Summary: mike.anderson can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [8] Send-As Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        User Account: TECHCORP\nancy.rodriguez
        Inherited: False

    Summary: nancy.rodriguez can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [9] Send-As Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        User Account: TECHCORP\thomas.white
        Inherited: False

    Summary: thomas.white can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [10] Send-As Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        User Account: TECHCORP\stephanie.walker
        Inherited: False

    Summary: stephanie.walker can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [11] Send-As Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        User Account: TECHCORP\patricia.clark
        Inherited: False

    Summary: patricia.clark can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [12] Send-As Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        User Account: TECHCORP\kevin.lewis
        Inherited: False

    Summary: kevin.lewis can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [13] Send-As Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        User Account: TECHCORP\brian.hall
        Inherited: False

    Summary: brian.hall can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [14] Send-As Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        User Account: TECHCORP\michelle.young
        Inherited: False

    Summary: michelle.young can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [15] Send-As Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        User Account: TECHCORP\daniel.king
        Inherited: False

    Summary: daniel.king can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [16] Send-As Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        User Account: TECHCORP\carol.wright
        Inherited: False

    Summary: carol.wright can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [17] Send-As Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        User Account: TECHCORP\svc-compliance
        Inherited: False

    Summary: svc-compliance can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [18] Send-As Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        User Account: TECHCORP\audit.manager
        Inherited: False

    Summary: audit.manager can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [19] Send-As Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        User Account: TECHCORP\procurement.manager
        Inherited: False

    Summary: procurement.manager can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [20] Send-As Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        User Account: TECHCORP\facilities.admin
        Inherited: False

    Summary: facilities.admin can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [21] Send-As Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        User Account: SUBSIDIARY\external.user
        Inherited: False

    Summary: external.user can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [22] Send-As Entry:
        Mailbox: Chief Financial Officer
        Email: <EMAIL>
        User Account: TECHCORP\finance.manager
        Inherited: False

    Summary: finance.manager can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown

    [23] Send-As Entry:
        Mailbox: Chief Technology Officer
        Email: <EMAIL>
        User Account: TECHCORP\it.director
        Inherited: False

    Summary: it.director can <NAME_EMAIL>
    Account Type: User | Assignment Method: Direct | Created: Unknown | Status: Unknown


================================================================================
SEND-ON-BEHALF PERMISSIONS (MBX-5.1)
================================================================================

Control ID: MBX-5.1
Risk Level: Medium
CVSS Score: 6.2
Compliance Status: Compliant
Compliance Score: 88%
Sample Size: 50

FINDING:
  Send-On-Behalf permissions appear reasonable

RECOMMENDATION:
  Regularly review Send-On-Behalf permissions and ensure they align with current business needs

SEND-ON-BEHALF PERMISSIONS COMPREHENSIVE DETAILS:
  Total Send-On-Behalf Permissions: 34
  Unique Users with Send-On-Behalf: 28

  COMPLETE SEND-ON-BEHALF PERMISSIONS LISTING:
    [1] Send-On-Behalf Entry:
        Mailbox: Chief Financial Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\finance.manager

    Summary: finance.manager can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [2] Send-On-Behalf Entry:
        Mailbox: HR Director
        Email: <EMAIL>
        Delegate User: TECHCORP\sarah.johnson

    Summary: sarah.johnson can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [3] Send-On-Behalf Entry:
        Mailbox: Sales Manager
        Email: <EMAIL>
        Delegate User: TECHCORP\jennifer.garcia

    Summary: jennifer.garcia can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [4] Send-On-Behalf Entry:
        Mailbox: Chief Executive Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\executive.assistant

    Summary: executive.assistant can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [5] Send-On-Behalf Entry:
        Mailbox: Chief Technology Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\it.director

    Summary: it.director can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [6] Send-On-Behalf Entry:
        Mailbox: Marketing Department
        Email: <EMAIL>
        Delegate User: TECHCORP\lisa.brown

    Summary: lisa.brown can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [7] Send-On-Behalf Entry:
        Mailbox: Operations Manager
        Email: <EMAIL>
        Delegate User: TECHCORP\michelle.young

    Summary: michelle.young can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [8] Send-On-Behalf Entry:
        Mailbox: Legal Department
        Email: <EMAIL>
        Delegate User: TECHCORP\amanda.taylor

    Summary: amanda.taylor can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [9] Send-On-Behalf Entry:
        Mailbox: Procurement Team
        Email: <EMAIL>
        Delegate User: TECHCORP\thomas.white

    Summary: thomas.white can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [10] Send-On-Behalf Entry:
        Mailbox: Quality Assurance
        Email: <EMAIL>
        Delegate User: TECHCORP\patricia.clark

    Summary: patricia.clark can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [11] Send-On-Behalf Entry:
        Mailbox: Research & Development
        Email: <EMAIL>
        Delegate User: TECHCORP\kevin.lewis

    Summary: kevin.lewis can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [12] Send-On-Behalf Entry:
        Mailbox: Training Department
        Email: <EMAIL>
        Delegate User: TECHCORP\stephanie.walker

    Summary: stephanie.walker can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [13] Send-On-Behalf Entry:
        Mailbox: Security Team
        Email: <EMAIL>
        Delegate User: TECHCORP\brian.hall

    Summary: brian.hall can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [14] Send-On-Behalf Entry:
        Mailbox: Accounting Department
        Email: <EMAIL>
        Delegate User: TECHCORP\daniel.king

    Summary: daniel.king can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [15] Send-On-Behalf Entry:
        Mailbox: Project Management Office
        Email: <EMAIL>
        Delegate User: TECHCORP\carol.wright

    Summary: carol.wright can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [16] Send-On-Behalf Entry:
        Mailbox: Facilities Management
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin

    Summary: facilities.admin can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [17] Send-On-Behalf Entry:
        Mailbox: Customer Service
        Email: <EMAIL>
        Delegate User: TECHCORP\nancy.rodriguez

    Summary: nancy.rodriguez can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [18] Send-On-Behalf Entry:
        Mailbox: IT Support
        Email: <EMAIL>
        Delegate User: TECHCORP\mike.anderson

    Summary: mike.anderson can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [19] Send-On-Behalf Entry:
        Mailbox: Compliance Officer
        Email: <EMAIL>
        Delegate User: TECHCORP\svc-compliance

    Summary: svc-compliance can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [20] Send-On-Behalf Entry:
        Mailbox: Internal Audit
        Email: <EMAIL>
        Delegate User: TECHCORP\audit.manager

    Summary: audit.manager can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [21] Send-On-Behalf Entry:
        Mailbox: Vendor Communications
        Email: <EMAIL>
        Delegate User: TECHCORP\procurement.manager

    Summary: procurement.manager can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [22] Send-On-Behalf Entry:
        Mailbox: Shared Project Alpha
        Email: <EMAIL>
        Delegate User: SUBSIDIARY\external.user

    Summary: external.user can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [23] Send-On-Behalf Entry:
        Mailbox: John Doe
        Email: <EMAIL>
        Delegate User: TECHCORP\jane.smith

    Summary: jane.smith can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [24] Send-On-Behalf Entry:
        Mailbox: Finance Team
        Email: <EMAIL>
        Delegate User: TECHCORP\robert.wilson

    Summary: robert.wilson can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [25] Send-On-Behalf Entry:
        Mailbox: Sales Team
        Email: <EMAIL>
        Delegate User: TECHCORP\david.martinez

    Summary: david.martinez can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [26] Send-On-Behalf Entry:
        Mailbox: HR Shared Mailbox
        Email: <EMAIL>
        Delegate User: TECHCORP\sarah.johnson

    Summary: sarah.johnson can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [27] Send-On-Behalf Entry:
        Mailbox: Executive Board Room
        Email: <EMAIL>
        Delegate User: TECHCORP\executive.assistant

    Summary: executive.assistant can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [28] Send-On-Behalf Entry:
        Mailbox: Conference Room A
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin

    Summary: facilities.admin can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [29] Send-On-Behalf Entry:
        Mailbox: Conference Room B
        Email: <EMAIL>
        Delegate User: TECHCORP\facilities.admin

    Summary: facilities.admin can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [30] Send-On-Behalf Entry:
        Mailbox: General Information
        Email: <EMAIL>
        Delegate User: TECHCORP\customer.service.lead

    Summary: customer.service.lead can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [31] Send-On-Behalf Entry:
        Mailbox: Technical Support
        Email: <EMAIL>
        Delegate User: TECHCORP\support.manager

    Summary: support.manager can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [32] Send-On-Behalf Entry:
        Mailbox: No Reply System
        Email: <EMAIL>
        Delegate User: TECHCORP\system.admin

    Summary: system.admin can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [33] Send-On-Behalf Entry:
        Mailbox: System Alerts
        Email: <EMAIL>
        Delegate User: TECHCORP\monitoring.service

    Summary: monitoring.service can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown

    [34] Send-On-Behalf Entry:
        Mailbox: Backup Reports
        Email: <EMAIL>
        Delegate User: TECHCORP\svc-backup

    Summary: svc-backup can send on <NAME_EMAIL>
    Account Type: User | Assignment Method: Unknown | Created: Unknown | Status: Unknown


================================================================================
ADMINISTRATOR DISCOVERY
================================================================================

EXCHANGE ROLE ASSIGNMENTS:
  Total Role Assignments: 6

  [1] Role Assignment:
      Role Assignee: TECHCORP\admin-exchange
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Organization Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: Direct

  [2] Role Assignment:
      Role Assignee: TECHCORP\svc-compliance
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Discovery Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Read-Only-Administrator
      Assignment Method: Direct

  [3] Role Assignment:
      Role Assignee: TECHCORP\admin-server
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Server Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: RoleGroup

  [4] Role Assignment:
      Role Assignee: TECHCORP\admin-recipient
      Assignee Domain: techcorp.com
      Assignee Type: User
      Role: Recipient Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Mailbox-Administrator
      Assignment Method: RoleGroup

  [5] Role Assignment:
      Role Assignee: SUBSIDIARY\external.auditor
      Assignee Domain: subsidiary.techcorp.com
      Assignee Type: User
      Role: View-Only Organization Management
      Role Type: Management
      Scope Type: Organization-Wide
      Affected Domains: All
      Organization-Wide: True
      Is Enabled: True
      Admin Classification: Read-Only-Administrator
      Assignment Method: Direct

  [6] Role Assignment:
      Role Assignee: SUBSIDIARY\finance.auditor
      Assignee Domain: subsidiary.techcorp.com
      Assignee Type: User
      Role: Discovery Management
      Role Type: Management
      Scope Type: Custom-Scope
      Affected Domains: techcorp.com
      Organization-Wide: False
      Is Enabled: True
      Admin Classification: Exchange-Administrator
      Assignment Method: Direct

ADMINISTRATOR DOMAIN MAPPING:
  Domain: techcorp.com
    Administrator Count: 4
      - TECHCORP\admin-exchange (Exchange-Administrator)
      - TECHCORP\svc-compliance (Read-Only-Administrator)
      - TECHCORP\admin-server (Exchange-Administrator)
      - TECHCORP\admin-recipient (Mailbox-Administrator)

  Domain: subsidiary.techcorp.com
    Administrator Count: 2
      - SUBSIDIARY\external.auditor (Read-Only-Administrator)
      - SUBSIDIARY\finance.auditor (Exchange-Administrator)

CROSS-DOMAIN ADMINISTRATIVE RELATIONSHIPS:
  [1] Cross-Domain Admin Relationship:
      Admin Domain: subsidiary.techcorp.com
      Target Domain: techcorp.com
      Relationship Type: Cross-Domain-Administration
      Risk Level: High
      Admin Count: 2

ADMINISTRATOR CLASSIFICATION SUMMARY:
  TECHCORP\admin-exchange: Exchange-Administrator
  TECHCORP\svc-compliance: Read-Only-Administrator
  TECHCORP\svc-backup: Business-User
  TECHCORP\svc-monitoring: Business-User
  TECHCORP\admin-server: Exchange-Administrator
  TECHCORP\admin-recipient: Mailbox-Administrator
  SUBSIDIARY\external.auditor: Read-Only-Administrator
  SUBSIDIARY\finance.auditor: Exchange-Administrator

CLASSIFICATION SUMMARY:
  Business-User: 2 administrators
  Mailbox-Administrator: 1 administrators
  Exchange-Administrator: 3 administrators
  Read-Only-Administrator: 2 administrators

================================================================================
END OF REPORT
================================================================================

Report Generation Completed: 2025-09-19 16:43:52
Total Processing Time: 00:00:00.2434791

This comprehensive report contains all detailed audit information including:
  - Complete permission listings for all security controls
  - Full user and mailbox metadata for each permission assignment
  - Detailed audit logging configuration and compliance status
  - Business justifications and assignment dates where available
  - Department and organizational context for all findings

Generated by: Exchange Audit Tools - E.Z. Consultancy
Script Version: 3.0.0
================================================================================
