{"AuditMetadata": {"AuditID": "b8f3e2d1-4c5a-6789-bcde-f012345678ab", "AuditStartTime": "2025-09-14 10:15:30", "ScriptVersion": "1.6.7", "ScriptReleaseDate": "September 11, 2025", "ScriptAuthor": "E.Z. Consultancy", "PowerShellVersion": "5.1.19041.4648", "PowerShellEdition": "Desktop", "OrganizationName": "TechCorp International", "ExchangeServers": 4, "TotalMailboxes": 847, "MaxMailboxSample": 500, "AssessmentScope": "5 Critical Mailbox Security Controls", "AuditUser": "svc-audit", "ComputerName": "EXCH-MGMT-02", "DomainFilterEnabled": true, "FilteredDomains": ["techcorp.com", "subsidiary.techcorp.com"], "DomainFilterScope": "Domain-specific audit for: techcorp.com, subsidiary.techcorp.com"}, "ComplianceSummary": {"AssessmentType": "Exchange Server Mailbox Security Controls Audit", "TotalControlsAssessed": 5, "SuccessfulAssessments": 5, "FailedAssessments": 0, "AuditStartTime": "2025-09-14 10:15:30", "AuditEndTime": "2025-09-14 10:32:45", "AuditDurationSeconds": 1035.67, "AuditID": "b8f3e2d1-4c5a-6789-bcde-f012345678ab", "FrameworkVersion": "Exchange Mailbox Security Controls v1.0", "AuditType": "Read-Only Assessment - Production Safe", "AuditExecutedBy": "svc-audit", "ComputerName": "EXCH-MGMT-02", "PowerShellVersion": "5.1.19041.4648"}, "MBX_1_1_ImpersonationRights": {"ControlID": "MBX-1.1", "ControlName": "Mailbox Impersonation Rights", "RiskLevel": "Critical", "CVSSScore": 9.3, "TotalImpersonationAssignments": 4, "ImpersonationUsers": [{"RoleAssignee": "TECHCORP\\svc-exchange-backup", "RoleAssigneeName": "Exchange Backup Service", "RoleAssigneeType": "User", "AssignmentMethod": "Direct", "IsValid": true, "WhenCreated": "2025-01-15 09:30:00"}, {"RoleAssignee": "TECHCORP\\svc-compliance", "RoleAssigneeName": "Compliance Service Account", "RoleAssigneeType": "User", "AssignmentMethod": "Direct", "IsValid": true, "WhenCreated": "2025-02-20 14:15:00"}, {"RoleAssignee": "TECHCORP\\admin-exchange", "RoleAssigneeName": "Exchange Administrator", "RoleAssigneeType": "User", "AssignmentMethod": "RoleGroup", "IsValid": true, "WhenCreated": "2025-01-10 11:45:00"}, {"RoleAssignee": "TECHCORP\\svc-monitoring", "RoleAssigneeName": "Exchange Monitoring Service", "RoleAssigneeType": "User", "AssignmentMethod": "Direct", "IsValid": true, "WhenCreated": "2025-03-05 16:20:00"}], "CurrentValue": "4 accounts with ApplicationImpersonation rights", "BaselineValue": "Minimal impersonation rights with regular review", "ComplianceStatus": "Review Required", "ComplianceScore": 75, "Finding": "Limited impersonation rights - review assignments", "Recommendation": "Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions", "AssessmentDate": "2025-09-14 10:15:30"}, "MBX_2_1_FullAccessPermissions": {"ControlID": "MBX-2.1", "ControlName": "Mailbox Full Access Permissions", "RiskLevel": "High", "CVSSScore": 8.1, "SampleSize": 50, "TotalFullAccessPermissions": 89, "FullAccessPermissions": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "<PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\jane.smith", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\executive.assistant", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\robert.wilson", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "HR Shared Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\sarah.johnson", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "IT Support", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\mike.anderson", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Marketing Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\lisa.brown", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\david.martinez", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\amanda.taylor", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Procurement Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\thomas.white", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Quality Assurance", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\patricia.clark", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Customer Service", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\nancy.rodriguez", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Research & Development", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\kevin.lewis", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Training Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\stephanie.walker", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Security Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\brian.hall", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Operations Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\michelle.young", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Accounting Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\daniel.king", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Project Management Office", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\carol.wright", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Facilities Management", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\facilities.admin", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Conference Room A", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\facilities.admin", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Conference Room B", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\facilities.admin", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Executive Board Room", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\executive.assistant", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Shared Project Alpha", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "SUBSIDIARY\\external.user", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Vendor Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\procurement.manager", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Compliance Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\svc-compliance", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Internal Audit", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\audit.manager", "AccessRights": "FullAccess", "IsInherited": false, "Deny": false}], "UniqueUsersWithFullAccess": 67, "CurrentValue": "89 Full Access permissions found across 50 mailboxes", "BaselineValue": "Minimal Full Access delegations with business justification", "ComplianceStatus": "Review Required", "ComplianceScore": 72, "Finding": "High number of Full Access permissions - review required", "Recommendation": "Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.", "AssessmentDate": "2025-09-14 10:15:30", "CrossDomainAnalysis": {"CrossDomainRelationships": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Shared Project Alpha", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\external.user", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Business-User-Delegate", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 5, "RiskLevel": "High", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Vendor Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\vendor.admin", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Business-User-Delegate", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 4, "RiskLevel": "Medium", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\finance.auditor", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Exchange-Administrator", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 6, "RiskLevel": "High", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Compliance Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\compliance.manager", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Read-Only-Administrator", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 4, "RiskLevel": "Medium", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\board.secretary", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Business-User-Delegate", "AccessRights": "Send-As", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 7, "RiskLevel": "High", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\legal.counsel", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Business-User-Delegate", "AccessRights": "Send-On-Behalf", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 3, "RiskLevel": "Medium", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Internal Audit", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\external.auditor", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Exchange-Administrator", "AccessRights": "FullAccess", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 6, "RiskLevel": "High", "PermissionType": "Direct"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "HR Shared Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxDomain": "techcorp.com", "AdminUser": "SUBSIDIARY\\hr.consultant", "AdminDomain": "subsidiary.techcorp.com", "AdminClassification": "Business-User-Delegate", "AccessRights": "Send-As", "IsInherited": false, "IsCrossDomain": true, "RiskScore": 4, "RiskLevel": "Medium", "PermissionType": "Direct"}], "PermissionSummary": {"subsidiary.techcorp.com -> techcorp.com": {"Count": 8, "AdminDomain": "subsidiary.techcorp.com", "MailboxDomain": "techcorp.com", "HighRiskCount": 4, "AdminTypes": {"Business-User-Delegate": 5, "Exchange-Administrator": 2, "Read-Only-Administrator": 1}}}, "RiskAssessment": {"TotalPermissionRelationships": 89, "CrossDomainRelationships": 8, "HighRiskRelationships": 4, "CrossDomainPercentage": 8.99, "RiskDistribution": {"High": 4, "Medium": 4, "Low": 81}}}}, "MBX_3_1_AuditLogging": {"ControlID": "MBX-3.1", "ControlName": "Mailbox Audit Logging Configuration", "RiskLevel": "Medium", "CVSSScore": 6.8, "AdminAuditLogEnabled": true, "MailboxAuditBypassUsers": [{"Name": "svc-exchange-backup", "AuditBypassEnabled": true}, {"Name": "svc-monitoring", "AuditBypassEnabled": true}], "AuditBypassCount": 2, "SampleMailboxAuditSettings": [{"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Chief Executive Officer", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Finance Team", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Chief Financial Officer", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Chief Technology Officer", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "HR Shared Mailbox", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "IT Support", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Marketing Department", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Sales Team", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Legal Department", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Procurement Team", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Quality Assurance", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Customer Service", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Research & Development", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Training Department", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Security Team", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Operations Manager", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Accounting Department", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Project Management Office", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Facilities Management", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Conference Room A", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Conference Room B", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Executive Board Room", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Shared Project Alpha", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Vendor Communications", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Compliance Officer", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Internal Audit", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "<PERSON>", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Executive Assistant", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Finance Manager", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "IT Director", "AuditEnabled": true, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Test Mailbox", "AuditEnabled": false, "AuditLogAgeLimit": "90.00:00:00"}, {"Identity": "<EMAIL>", "DisplayName": "Legacy System Account", "AuditEnabled": false, "AuditLogAgeLimit": "90.00:00:00"}], "AuditEnabledMailboxes": 48, "SampleSize": 50, "CurrentValue": "Admin audit: True, Mailbox audit enabled: 48 of 50 sampled", "BaselineValue": "Admin and mailbox audit logging enabled organization-wide", "ComplianceStatus": "Review Required", "ComplianceScore": 96, "Finding": "Admin audit logging enabled", "Recommendation": "Ensure mailbox audit is enabled for all mailboxes", "AssessmentDate": "2025-09-14 10:15:30"}, "MBX_4_1_SendAsPermissions": {"ControlID": "MBX-4.1", "ControlName": "Send-As Permissions", "RiskLevel": "High", "CVSSScore": 7.6, "SampleSize": 50, "TotalSendAsPermissions": 23, "SendAsPermissions": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\executive.assistant", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\robert.wilson", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Marketing Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\lisa.brown", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\david.martinez", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "HR Shared Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\sarah.johnson", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\amanda.taylor", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "IT Support", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\mike.anderson", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Customer Service", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\nancy.rodriguez", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Procurement Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\thomas.white", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Training Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\stephanie.walker", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Quality Assurance", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\patricia.clark", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Research & Development", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\kevin.lewis", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Security Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\brian.hall", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Operations Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\michelle.young", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Accounting Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\daniel.king", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Project Management Office", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\carol.wright", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Compliance Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\svc-compliance", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Internal Audit", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\audit.manager", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Vendor Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\procurement.manager", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Facilities Management", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\facilities.admin", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Shared Project Alpha", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "SUBSIDIARY\\external.user", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Financial Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\finance.manager", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Technology Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "TECHCORP\\it.director", "ExtendedRights": "Send-As", "AccessControlType": "Allow", "IsInherited": false}], "UniqueUsersWithSendAs": 18, "CurrentValue": "23 Send-As permissions found across 50 mailboxes", "BaselineValue": "Minimal Send-As permissions with business justification", "ComplianceStatus": "Compliant", "ComplianceScore": 85, "Finding": "Send-As permissions appear reasonable", "Recommendation": "Regularly review Send-As permissions and ensure they are justified by business requirements", "AssessmentDate": "2025-09-14 10:15:30"}, "MBX_5_1_SendOnBehalfPermissions": {"ControlID": "MBX-5.1", "ControlName": "Send-On-Behalf Permissions", "RiskLevel": "Medium", "CVSSScore": 6.2, "SampleSize": 50, "TotalSendOnBehalfPermissions": 34, "SendOnBehalfPermissions": [{"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Financial Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\finance.manager", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "HR Director", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\sarah.johnson", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\jennifer.garcia", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Executive Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\executive.assistant", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Chief Technology Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\it.director", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Marketing Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\lisa.brown", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Operations Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\michelle.young", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\amanda.taylor", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Procurement Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\thomas.white", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Quality Assurance", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\patricia.clark", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Research & Development", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\kevin.lewis", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Training Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\stephanie.walker", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Security Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\brian.hall", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Accounting Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\daniel.king", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Project Management Office", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\carol.wright", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Facilities Management", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\facilities.admin", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Customer Service", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\nancy.rodriguez", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "IT Support", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\mike.anderson", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Compliance Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\svc-compliance", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Internal Audit", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\audit.manager", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Vendor Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\procurement.manager", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Shared Project Alpha", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "SUBSIDIARY\\external.user", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "<PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\jane.smith", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Finance Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\robert.wilson", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Sales Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\david.martinez", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "HR Shared Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\sarah.johnson", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Executive Board Room", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\executive.assistant", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Conference Room A", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\facilities.admin", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Conference Room B", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\facilities.admin", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "General Information", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\customer.service.lead", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Technical Support", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\support.manager", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "No Reply System", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\system.admin", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "System Alerts", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\monitoring.service", "PermissionType": "Send-On-Behalf"}, {"MailboxIdentity": "<EMAIL>", "MailboxDisplayName": "Backup Reports", "MailboxPrimarySmtpAddress": "<EMAIL>", "DelegateUser": "TECHCORP\\svc-backup", "PermissionType": "Send-On-Behalf"}], "UniqueUsersWithSendOnBehalf": 28, "CurrentValue": "34 Send-On-Behalf permissions found across 50 mailboxes", "BaselineValue": "Minimal Send-On-Behalf permissions with business justification", "ComplianceStatus": "Compliant", "ComplianceScore": 88, "Finding": "Send-On-Behalf permissions appear reasonable", "Recommendation": "Regularly review Send-On-Behalf permissions and ensure they align with current business needs", "AssessmentDate": "2025-09-14 10:15:30"}, "AdministratorDiscovery": {"RoleAssignments": [{"RoleAssignee": "TECHCORP\\admin-exchange", "AssigneeDomain": "techcorp.com", "AssigneeType": "User", "Role": "Organization Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "Direct"}, {"RoleAssignee": "TECHCORP\\svc-compliance", "AssigneeDomain": "techcorp.com", "AssigneeType": "User", "Role": "Discovery Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "Direct"}, {"RoleAssignee": "TECHCORP\\admin-server", "AssigneeDomain": "techcorp.com", "AssigneeType": "User", "Role": "Server Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "TECHCORP\\admin-recipient", "AssigneeDomain": "techcorp.com", "AssigneeType": "User", "Role": "Recipient Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Mailbox-Administrator", "AssignmentMethod": "RoleGroup"}, {"RoleAssignee": "SUBSIDIARY\\external.auditor", "AssigneeDomain": "subsidiary.techcorp.com", "AssigneeType": "User", "Role": "View-Only Organization Management", "RoleType": "Management", "Scope": {"Type": "Organization-Wide", "AffectedDomains": ["All"], "IsOrganizationWide": true}, "IsEnabled": true, "AdminClassification": "Read-Only-Administrator", "AssignmentMethod": "Direct"}, {"RoleAssignee": "SUBSIDIARY\\finance.auditor", "AssigneeDomain": "subsidiary.techcorp.com", "AssigneeType": "User", "Role": "Discovery Management", "RoleType": "Management", "Scope": {"Type": "Custom-Scope", "AffectedDomains": ["techcorp.com"], "IsOrganizationWide": false}, "IsEnabled": true, "AdminClassification": "Exchange-Administrator", "AssignmentMethod": "Direct"}], "AdminDomainMap": {"techcorp.com": [{"RoleAssignee": "TECHCORP\\admin-exchange", "AdminClassification": "Exchange-Administrator"}, {"RoleAssignee": "TECHCORP\\svc-compliance", "AdminClassification": "Read-Only-Administrator"}, {"RoleAssignee": "TECHCORP\\admin-server", "AdminClassification": "Exchange-Administrator"}, {"RoleAssignee": "TECHCORP\\admin-recipient", "AdminClassification": "Mailbox-Administrator"}], "subsidiary.techcorp.com": [{"RoleAssignee": "SUBSIDIARY\\external.auditor", "AdminClassification": "Read-Only-Administrator"}, {"RoleAssignee": "SUBSIDIARY\\finance.auditor", "AdminClassification": "Exchange-Administrator"}]}, "CrossDomainRelationships": [{"AdminDomain": "subsidiary.techcorp.com", "TargetDomain": "techcorp.com", "RelationshipType": "Cross-Domain-Administration", "RiskLevel": "High", "AdminCount": 2}], "AdminClassification": {"TECHCORP\\admin-exchange": "Exchange-Administrator", "TECHCORP\\svc-compliance": "Read-Only-Administrator", "TECHCORP\\svc-backup": "Business-User", "TECHCORP\\svc-monitoring": "Business-User", "TECHCORP\\admin-server": "Exchange-Administrator", "TECHCORP\\admin-recipient": "Mailbox-Administrator", "SUBSIDIARY\\external.auditor": "Read-Only-Administrator", "SUBSIDIARY\\finance.auditor": "Exchange-Administrator"}}}